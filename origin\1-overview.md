# Memos 项目概览

<details>
<summary>📁 主要源文件 (点击展开)</summary>

**核心配置文件:**

- [README.md](https://github.com/usememos/memos/blob/50a41a39/README.md) - 项目说明
- [go.mod](https://github.com/usememos/memos/blob/50a41a39/go.mod) - Go依赖管理
- [web/package.json](https://github.com/usememos/memos/blob/50a41a39/web/package.json) - 前端依赖管理

**服务器核心:**

- [server/server.go](https://github.com/usememos/memos/blob/50a41a39/server/server.go) - 服务器主入口

**API定义:**

- [proto/api/v1/markdown_service.proto](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/markdown_service.proto) - Markdown服务API定义
- [server/router/api/v1/markdown_service.go](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/markdown_service.go) - Markdown服务实现

**前端组件:**

- [web/src/main.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx) - 前端入口
- [web/src/components/MemoContent/CodeBlock.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/components/MemoContent/CodeBlock.tsx) - 代码块组件

</details>

---

## 概览

Memos 是一个注重隐私的、自托管的知识管理平台，使用户能够通过全面的 Markdown 支持来捕获、组织和分享想法。本页面介绍了应用程序的核心功能、技术栈和高级架构。

如需了解详细的前端实现，请参阅 [前端](/usememos/memos/3-frontend)。如需了解后端服务和 API，请参阅 [后端](/usememos/memos/4-backend)。如需了解部署策略，请参阅 [部署](/usememos/memos/5-deployment)。

## 应用架构

Memos 遵循现代全栈架构，具有 React 前端、Go 后端和灵活的数据持久化。应用程序通过使用连接多路复用的单个服务器进程同时提供 HTTP 和 gRPC 流量。

### 整体系统架构

**来源：** [server/server.go 31-101](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L31-L101) [README.md 32-56](https://github.com/usememos/memos/blob/50a41a39/README.md#L32-L56)

```mermaid
graph TB
    %% 客户端层
    subgraph "Client Layer"
        A[React Web Frontend<br/>Vite + MobX + React Router]
        B[Mobile Browsers]
    end
    
    %% API网关层
    subgraph "API Gateway Layer"
        C[gRPC-Gateway<br/>HTTP/JSON ↔ gRPC]
        D[Authentication Layer<br/>Session + JWT]
    end
    
    %% 后端服务层
    subgraph "Backend Services"
        E[Go Backend Server<br/>Echo + gRPC]
        F[MemoService<br/>CRUD Operations]
        G[UserService<br/>User Management]
        H[AuthService<br/>Session Management]
        I[MarkdownService<br/>Content Processing]
        J[WorkspaceService<br/>Settings Management]
    end
    
    %% 数据层
    subgraph "Data Layer"
        K[Store Interface<br/>Database Abstraction]
        L[SQLite Database]
        M[MySQL Database]
        N[PostgreSQL Database]
        O[Local File Storage<br/>Attachments & Media]
    end
    
    %% 外部服务
    subgraph "External Services"
        P[SSO Providers<br/>OAuth2/OIDC]
        Q[External Webhooks<br/>Notifications]
    end
    
    %% 连接关系
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K
    K --> L
    K --> M
    K --> N
    K --> O
    H --> P
    E --> Q
    
    %% 样式
    classDef clientLayer fill:#e1f5fe
    classDef apiLayer fill:#f3e5f5
    classDef backendLayer fill:#e8f5e8
    classDef dataLayer fill:#fff3e0
    classDef externalLayer fill:#fce4ec
    
    class A,B clientLayer
    class C,D apiLayer
    class E,F,G,H,I,J backendLayer
    class K,L,M,N,O dataLayer
    class P,Q externalLayer
```

### 服务器基础设施和服务组件

后端服务器使用 `cmux` 在单个端口上多路复用 HTTP 和 gRPC 流量，服务围绕域边界组织：

**来源：** [server/server.go 84-101](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L84-L101) [server/server.go 117-139](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L117-L139) [server/router/api/v1/markdown_service.go 1-55](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/markdown_service.go#L1-L55)

```mermaid
graph TB
    %% HTTP/gRPC服务器层
    subgraph "HTTP/gRPC Server"
        A[Echo HTTP Server<br/>Static Files + Health]
        B[gRPC Server<br/>Protocol Buffer APIs]
        C[Connection Multiplexer<br/>Single Port for HTTP + gRPC]
    end

    %% 核心服务层
    subgraph "Core Services"
        D[AuthService<br/>Sessions + JWT]
        E[MemoService<br/>CRUD + Relations + Tags]
        F[UserService<br/>Accounts + Settings]
        G[MarkdownService<br/>Parsing + Rendering]
        H[WorkspaceService<br/>Global Settings]
    end

    %% 中间件和安全层
    subgraph "Middleware & Security"
        I[Auth Interceptor<br/>Session Validation]
        J[Access Control<br/>Role-based Permissions]
        K[CORS Handler]
    end

    %% 数据处理层
    subgraph "Data Processing"
        L[Gomark Library<br/>Markdown Processing]
        M[Content Parser<br/>Tags, Links, Tasks]
        N[Webhook Dispatcher<br/>Event Notifications]
    end

    %% 存储接口层
    subgraph "Storage Interface"
        O[Store Interface<br/>Database Abstraction]
        P[SQLite Driver]
        Q[MySQL Driver]
        R[PostgreSQL Driver]
        S[Schema Migration<br/>Version Management]
    end

    %% 连接关系
    C --> A
    C --> B
    A --> I
    B --> I
    I --> J
    J --> K
    K --> D
    K --> E
    K --> F
    K --> G
    K --> H
    G --> L
    E --> M
    E --> N
    D --> O
    E --> O
    F --> O
    G --> O
    H --> O
    O --> P
    O --> Q
    O --> R
    O --> S

    %% 样式
    classDef serverLayer fill:#e3f2fd
    classDef coreLayer fill:#e8f5e8
    classDef middlewareLayer fill:#fff3e0
    classDef processingLayer fill:#f3e5f5
    classDef storageLayer fill:#fce4ec

    class A,B,C serverLayer
    class D,E,F,G,H coreLayer
    class I,J,K middlewareLayer
    class L,M,N processingLayer
    class O,P,Q,R,S storageLayer
```

## 技术栈和依赖

Memos 使用现代技术，为可靠性和性能选择特定的库：

### 技术栈概览

```mermaid
graph LR
    %% 前端技术栈
    subgraph "Frontend Stack"
        A[React 18.3.1<br/>UI Framework]
        B[Vite 6.3.5<br/>Build System]
        C[MobX 6.13.7<br/>State Management]
        D[React Router 7.6.1<br/>Client Navigation]
        E[Material-UI Joy<br/>UI Components]
        F[TailwindCSS 4.1.8<br/>Styling]
        G[Highlight.js<br/>Code Highlighting]
        H[KaTeX<br/>Math Notation]
        I[Lucide React<br/>Icons]
        J[i18next<br/>Internationalization]
    end

    %% 后端技术栈
    subgraph "Backend Stack"
        K[Go 1.24+<br/>Runtime]
        L[Echo v4<br/>HTTP Framework]
        M[gRPC<br/>RPC Framework]
        N[gRPC-Gateway v2<br/>API Gateway]
        O[GoMark<br/>Markdown Parser]
        P[JWT v5<br/>Authentication]
        Q[AWS SDK v2<br/>File Storage]
    end

    %% 数据库支持
    subgraph "Database Support"
        R[SQLite<br/>Embedded DB]
        S[MySQL<br/>Relational DB]
        T[PostgreSQL<br/>Enterprise DB]
    end

    %% 部署和工具
    subgraph "Deployment & Tools"
        U[Docker<br/>Containerization]
        V[pnpm<br/>Package Manager]
        W[Protocol Buffers<br/>API Definition]
        X[cmux<br/>Connection Multiplexing]
    end

    %% 连接关系
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J

    K --> L
    K --> M
    L --> N
    K --> O
    K --> P
    K --> Q

    N --> R
    N --> S
    N --> T

    L --> X
    M --> X

    %% 样式
    classDef frontend fill:#e1f5fe
    classDef backend fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef deployment fill:#f3e5f5

    class A,B,C,D,E,F,G,H,I,J frontend
    class K,L,M,N,O,P,Q backend
    class R,S,T database
    class U,V,W,X deployment
```

### 后端依赖

| 组件 | 库/版本 | 用途 |
|------|---------|------|
| 运行时 | Go 1.24+ | 高性能服务器实现 |
| HTTP 框架 | `github.com/labstack/echo/v4` | REST API 和静态文件服务 |
| gRPC 框架 | `google.golang.org/grpc` | 类型安全的服务定义 |
| API 网关 | `github.com/grpc-ecosystem/grpc-gateway/v2` | gRPC 到 REST 转换 |
| 数据库驱动 | `modernc.org/sqlite`, `github.com/go-sql-driver/mysql`, `github.com/lib/pq` | 多数据库支持 |
| Markdown 处理 | `github.com/usememos/gomark` | 自定义 Markdown 解析器和渲染器 |
| 认证 | `github.com/golang-jwt/jwt/v5` | JWT 令牌管理 |
| 文件存储 | `github.com/aws/aws-sdk-go-v2` | S3 兼容存储集成 |

### 前端依赖

| 组件 | 库/版本 | 用途 |
|------|---------|------|
| 框架 | `react@^18.3.1` | 现代 UI 框架 |
| 构建系统 | `vite@^6.3.5` | 快速开发和打包 |
| 状态管理 | `mobx@^6.13.7`, `mobx-react-lite@^4.1.0` | 响应式状态管理 |
| 路由 | `react-router-dom@^7.6.1` | 客户端导航 |
| UI 组件 | `@mui/joy@5.0.0-beta.52` | Material Design 组件 |
| 样式 | `tailwindcss@^4.1.8` | 实用优先的 CSS 框架 |
| 代码高亮 | `highlight.js@^11.11.1` | 代码块语法高亮 |
| Markdown 渲染 | `@matejmazur/react-katex@^3.1.3` | 数学符号支持 |
| 图标 | `lucide-react@^0.486.0` | 图标库 |
| 国际化 | `i18next@^25.2.1`, `react-i18next@^15.5.2` | 多语言支持 |

**来源：** [go.mod 1-99](https://github.com/usememos/memos/blob/50a41a39/go.mod#L1-L99) [web/package.json 1-82](https://github.com/usememos/memos/blob/50a41a39/web/package.json#L1-L82)

## 关键功能和能力

### 数据隐私和自托管

Memos 通过完整的自托管功能优先考虑数据所有权：

- **完整数据所有权**：所有应用数据存储在用户控制的数据库中
- **无外部依赖**：运行时操作不需要第三方云服务
- **自托管架构**：完全控制数据基础设施和访问策略

### 内容创建和管理

平台提供基于 Markdown 的全面内容管理：

- **纯文本效率**：简化的文本输入，具有即时保存功能
- **高级 Markdown 支持**：通过 `highlight.js` 进行全面的 Markdown 渲染和语法高亮
- **丰富媒体集成**：支持图片、链接和嵌入内容
- **特殊内容类型**：支持数学符号 (`katex`)、图表 (`mermaid`) 和代码块

### 技术卓越性

Memos 通过架构选择提供高性能：

- **高性能后端**：使用 Go 构建，优化资源利用率和可扩展性
- **现代前端**：基于 React.js 的用户界面，通过 `tailwindcss` 实现响应式设计
- **轻量级部署**：最小系统要求，高效资源消耗
- **跨平台兼容性**：支持 Linux、macOS、Windows 和容器化环境

### API 优先设计

应用程序提供全面的 API 访问：

- **RESTful API**：通过 `grpc-gateway` 提供 HTTP/JSON 端点，实现 Web 兼容性
- **gRPC 服务**：用于高性能访问的类型安全 Protocol Buffer 定义
- **多数据库支持**：兼容 SQLite、PostgreSQL 和 MySQL 数据库
- **Webhook 集成**：用于外部系统集成的事件驱动通知

**来源：** [README.md 38-67](https://github.com/usememos/memos/blob/50a41a39/README.md#L38-L67) [web/src/components/MemoContent/CodeBlock.tsx 1-81](https://github.com/usememos/memos/blob/50a41a39/web/src/components/MemoContent/CodeBlock.tsx#L1-L81) [proto/api/v1/markdown_service.proto 1-330](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/markdown_service.proto#L1-L330)

## 快速开始和部署

Memos 支持多种部署方法以满足不同的基础设施需求：

### Docker 部署（推荐）

主要部署方法使用 Docker 容器：

```bash
# 创建数据目录
mkdir -p ~/.memos

# 运行 Memos 容器
docker run -d \
  --name memos \
  --restart unless-stopped \
  -p 5230:5230 \
  -v ~/.memos:/var/opt/memos \
  neosmemo/memos:stable
```

在 `http://localhost:5230` 访问应用程序进行初始设置。

### 开发设置

本地开发需要单独的前端和后端进程：

**后端开发：**

```bash
# 安装 Go 依赖
go mod download

# 运行后端服务器
go run ./bin/memos/main.go --mode dev --port 8081
```

**前端开发：**

```bash
# 导航到 web 目录并安装依赖
cd web && pnpm install

# 启动开发服务器
pnpm dev
```

开发服务器运行在：

- 后端 API：`http://localhost:8081`
- 前端：`http://localhost:3001`

### 安装方法

- **容器部署**：Docker Hub 镜像位于 `neosmemo/memos`
- **二进制安装**：为 Linux、macOS 和 Windows 预编译的二进制文件
- **源码安装**：使用 Go 1.24+ 和 Node.js 22+ 从源码构建

**来源：** [README.md 76-125](https://github.com/usememos/memos/blob/50a41a39/README.md#L76-L125) [README.md 155-185](https://github.com/usememos/memos/blob/50a41a39/README.md#L155-L185)

## 项目状态和许可

Memos 在 MIT 许可证下积极维护，为个人和商业用途提供最大的灵活性。该项目欢迎社区在代码、文档、测试和本地化工作方面的贡献。

应用程序已准备好投入生产，具有稳定的核心功能，同时继续积极开发新功能和改进。

**来源：** [README.md 187-223](https://github.com/usememos/memos/blob/50a41a39/README.md#L187-223)

---

## 总结

本概览页面为你提供了 Memos 项目的全面技术视角，包括：

1. **完整的学习路径导航** - 后续章节的学习指引
2. **详细的架构图** - 整体系统架构、服务器组件架构和技术栈概览
3. **技术栈分析** - 前后端依赖库的具体版本和用途
4. **核心功能特性** - 数据隐私、内容管理、技术优势等
5. **部署和开发指南** - 实际操作的具体步骤

这为你后续使用 Vue.js 和 Python 复现这个项目提供了非常好的技术参考基础。
