# Memos 项目架构

> 📅 **最后索引**: 2025年6月30日 ([50a41a](https://github.com/usememos/memos/commits/50a41a39))

## 📁 主要源文件

<details>
<summary><strong>🔍 点击展开查看核心源文件列表</strong></summary>

**核心架构文件:**

- [server/server.go](https://github.com/usememos/memos/blob/50a41a39/server/server.go) - 服务器主架构
- [store/store.go](https://github.com/usememos/memos/blob/50a41a39/store/store.go) - 存储层抽象
- [web/src/main.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx) - 前端入口

**API 定义:**

- [proto/api/v1/markdown_service.proto](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/markdown_service.proto) - Markdown服务API定义
- [server/router/api/v1/markdown_service.go](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/markdown_service.go) - Markdown服务实现

**数据库驱动:**

- [store/db/sqlite/sqlite.go](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/sqlite.go) - SQLite驱动
- [store/db/mysql/mysql.go](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/mysql.go) - MySQL驱动

</details>

---

## 架构概述

本文档提供了 Memos 应用程序架构的全面概述，涵盖系统的前端、后端、数据存储和 API 层。它描述了基于 React 的前端如何通过 gRPC 服务与 Go 后端通信，以及模块化存储层如何支持多个数据库引擎。

如需了解部署相关信息，请参阅 [部署指南](./5-deployment.md)。如需了解开发设置详情，请参阅 [开发指南](./6-development-guide.md)。

## 系统概览

Memos 是一个全栈笔记应用程序，采用 React 前端和 Go 后端构建。系统使用 gRPC 进行内部通信，通过 gRPC-Gateway 暴露 HTTP/JSON 端点。架构通过统一的存储接口支持多个数据库后端。

### 整体系统架构

**来源：** [server/server.go 31-40](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L31-L40) [store/store.go 10-22](https://github.com/usememos/memos/blob/50a41a39/store/store.go#L10-L22) [web/src/main.tsx 16-30](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L16-L30)

```mermaid
flowchart TD
    %% Frontend Layer
    subgraph FL["Frontend Layer"]
        MSM["MobX State Management"]
        RR["React Router"]
        I18N["i18next<br/>Internationalization"]
        RA["React Application<br/>(Vite + TypeScript)"]
    end

    MSM --> RA
    RR --> RA
    I18N --> RA

    %% API Gateway
    subgraph AG["API Gateway"]
        GW["gRPC-Gateway<br/>(HTTP/JSON to gRPC)"]
        AUTH["Authentication<br/>JWT + Sessions"]
    end

    RA --> GW
    GW --> AUTH

    %% Backend Services
    subgraph BS["Backend Services"]
        SS["server.Server"]
        EHS["Echo HTTP Server"]
        GS["gRPC Server"]
        API1["APIV1Service"]
    end

    AUTH --> SS
    SS --> EHS
    SS --> GS
    GS --> API1

    %% Core Services
    subgraph CS["Core Services"]
        MEMO["MemoService"]
        USER["UserService"]
        AUTHS["AuthService"]
        MD["MarkdownService"]
        WS["WorkspaceService"]
    end

    API1 --> MEMO
    API1 --> USER
    API1 --> AUTHS
    API1 --> MD
    API1 --> WS

    %% Storage Layer
    subgraph SL["Storage Layer"]
        STORE["store.Store"]
        SDI["store.Driver Interface"]
        SQLITE["sqlite.DB"]
        MYSQL["mysql.DB"]
        POSTGRES["postgres.DB"]
    end

    MEMO --> STORE
    USER --> STORE
    AUTHS --> STORE
    MD --> STORE
    WS --> STORE

    STORE --> SDI

    SDI --> SQLITE
    SDI --> MYSQL
    SDI --> POSTGRES

    %% Data Persistence
    subgraph DP["Data Persistence"]
        SQLITEDB["SQLite Database"]
        MYSQLDB["MySQL Database"]
        POSTGRESDB["PostgreSQL Database"]
        LFS["Local File Storage"]
    end

    SQLITE --> SQLITEDB
    MYSQL --> MYSQLDB
    POSTGRES --> POSTGRESDB
    STORE --> LFS

    %% Styling
    classDef frontend fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000
    classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef backend fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef storage fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class FL,MSM,RR,I18N,RA frontend
    class AG,GW,AUTH_GROUP,AUTH api
    class BS,SS,EHS,GS,API1,CS,MEMO,USER,AUTHS,MD,WS backend
    class SL,STORE,SDI,SQLITE,MYSQL,POSTGRES storage
    class DP,SQLITEDB,MYSQLDB,POSTGRESDB,LFS data
```

## 前端架构

前端是一个使用现代工具和类型安全构建的 React 单页应用程序。它使用 MobX 进行响应式状态管理，并通过 gRPC-Web 协议与后端通信。

**来源：** [web/src/main.tsx 1-30](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L1-L30) [web/package.json 10-48](https://github.com/usememos/memos/blob/50a41a39/web/package.json#L10-L48) [web/src/components/MemoContent/CodeBlock.tsx 21-80](https://github.com/usememos/memos/blob/50a41a39/web/src/components/MemoContent/CodeBlock.tsx#L21-L80)

```mermaid
flowchart TD
    %% Build System
    subgraph BS["Build System"]
        VDS["Vite Dev Server<br/>& Build Tool"]
        TSC["TypeScript Compiler"]
        TWC["Tailwind CSS"]
    end

    %% Application Core
    subgraph AC["Application Core"]
        MA["Main Application"]
        RP["RouterProvider<br/>React Router"]
        CP["CssVarsProvider<br/>@mui/joy"]
    end

    VDS --> MA
    TSC --> MA
    TWC --> MA

    %% State Management
    subgraph SM["State Management"]
        US["userStore<br/>(User State)"]
        WS["workspaceStore<br/>(Workspace State)"]
        MS["memoStore<br/>(Memo Management)"]
        MFS["memoFilterStore<br/>(Search & Filters)"]
    end

    MA --> US
    MA --> WS
    MA --> MS
    MA --> MFS

    %% UI Components
    subgraph UI["UI Components"]
        MCC["MemoContent Components"]
        CB["CodeBlock"]
        MB["MermaidBlock"]
        LI["List/OrderedListItem/UnorderedListItem"]
    end

    MA --> MCC
    MA --> CB
    MA --> MB
    MA --> LI

    %% API Communication
    subgraph API["API Communication"]
        GWC["gRPC-Web Clients"]
        ASC["authServiceClient"]
        MSC["memoServiceClient"]
        USC["userServiceClient"]
        MDSC["markdownServiceClient"]
    end

    US --> GWC
    WS --> GWC
    MS --> GWC
    MFS --> GWC

    GWC --> ASC
    GWC --> MSC
    GWC --> USC
    GWC --> MDSC

    %% Internationalization
    subgraph I18N["Internationalization"]
        I18["i18next"]
        LF["18+ Language Files"]
    end

    MA --> I18
    I18 --> LF

    %% Styling
    classDef buildSystem fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef appCore fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef stateManagement fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef apiComm fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef uiComponents fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef i18n fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000

    class BS,VDS,TSC,TWC buildSystem
    class AC,MA,RP,CP appCore
    class SM,US,WS,MS,MFS stateManagement
    class API,GWC,ASC,MSC,USC,MDSC apiComm
    class UI,MCC,CB,MB,LI uiComponents
    class I18N,I18,LF i18n
```

前端遵循基于组件的架构，具有明确的关注点分离：

- **状态管理**：MobX 存储处理用户、工作空间、备忘录和过滤器的响应式状态
- **API 层**：gRPC-Web 客户端提供与后端服务的类型安全通信
- **UI 组件**：用于渲染具有 markdown 支持的备忘录内容的模块化 React 组件
- **构建系统**：Vite 提供快速开发和优化的生产构建

## 后端架构

后端实现了使用 Go 和 gRPC 服务的面向服务架构，通过原生 gRPC 和通过 gRPC-Gateway 的 HTTP/JSON 暴露。`server.Server` 结构体协调所有组件。

**来源：** [server/server.go 84-98](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L84-L98) [server/server.go 117-135](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L117-L135) [server/server.go 184-204](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L184-L204)

```mermaid
flowchart TD
    %% Server Infrastructure
    subgraph SI["Server Infrastructure"]
        SS["server.Server"]
    end

    %% Background Runners
    subgraph BR["Background Runners"]
        SPR["s3presign.Runner"]
        PP["profiler.Profiler"]
    end

    SS --> SPR
    SS --> PP

    %% Connection Multiplexer
    SS --> CM["cmux.New()<br/>Connection Multiplexer"]

    %% gRPC Path
    CM --> GS["gRPC Server"]

    %% Middleware & Security
    subgraph MS["Middleware & Security"]
        LI["LoggerInterceptor"]
        GRUI["grpcrecovery.UnaryServerInterceptor"]
        GAI["GRPCAuthInterceptor"]
    end

    GS --> LI
    GS --> GRUI
    GS --> GAI

    %% gRPC Services
    subgraph GRPCS["gRPC Services"]
        API1["apiv1.APIV1Service"]
    end

    LI --> API1
    GRUI --> API1
    GAI --> API1

    %% Service Layer
    subgraph SL["Service Layer"]
        MCO["Memo CRUD Operations"]
        UM["User Management"]
        AS["Authentication & Sessions"]
        MP["Markdown Processing"]
        WSS["Workspace Settings"]
    end

    API1 --> MCO
    API1 --> UM
    API1 --> AS
    API1 --> MP
    API1 --> WSS

    %% HTTP Path
    CM --> EHS["echo.Echo<br/>HTTP Server"]

    %% HTTP Layer
    subgraph HL["HTTP Layer"]
        HE["/healthz Endpoint"]
        FFS["frontend.FrontendService<br/>Static File Serving"]
        RSS["rss.RSSService<br/>RSS Feed Generation"]
    end

    EHS --> HE
    EHS --> FFS
    EHS --> RSS

    %% gRPC-Gateway Bridge
    FFS --> GGB["gRPC-Gateway<br/>HTTP/JSON Bridge"]
    RSS --> GGB

    %% Styling
    classDef serverInfra fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef background fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000
    classDef middleware fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef grpcServices fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef serviceLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef httpLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000

    class SI,SS,CM serverInfra
    class BR,SPR,PP background
    class GS,MS,LI,GRUI,GAI middleware
    class GRPCS,API1 grpcServices
    class SL,MCO,UM,AS,MP,WSS serviceLayer
    class EHS,HL,HE,FFS,RSS,GGB httpLayer
```

关键架构组件：

- **连接多路复用**：`cmux` 在单个端口上启用 HTTP 和 gRPC
- **服务注册**：所有 gRPC 服务实现 `APIV1Service` 接口
- **中间件链**：日志记录、恢复和身份验证拦截器处理所有请求
- **后台处理**：单独的 goroutine 处理 S3 预签名和性能分析

## 存储层架构

存储层实现了驱动程序模式，将数据库操作抽象在 `store.Driver` 接口后面，通过一致的缓存支持多个数据库后端。

**来源：** [store/driver.go 12-79](https://github.com/usememos/memos/blob/50a41a39/store/driver.go#L12-L79) [store/store.go 24-44](https://github.com/usememos/memos/blob/50a41a39/store/store.go#L24-L44) [store/db/sqlite/sqlite.go 16-52](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/sqlite.go#L16-L52) [store/db/mysql/mysql.go 14-41](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/mysql.go#L14-L41)

```mermaid
flowchart TD
    %% Store Abstraction
    subgraph SA["Store Abstraction"]
        SS["store.Store"]
    end

    %% Cache Layer
    subgraph CL["Cache Layer"]
        WSC["workspaceSettingCache<br/>cache.Cache"]
        UC["userCache<br/>cache.Cache"]
        USC["userSettingCache<br/>cache.Cache"]
    end

    SS --> WSC
    SS --> UC
    SS --> USC

    %% Driver Interface
    SS --> SDI["store.Driver Interface"]

    %% Driver Interface Methods
    subgraph DIM["Driver Interface Methods"]
        MEMO_OPS["CreateMemo()<br/>ListMemos()<br/>UpdateMemo()<br/>DeleteMemo()"]
        USER_OPS["CreateUser()<br/>UpdateUser()<br/>ListUsers()<br/>DeleteUser()"]
        WS_OPS["UpsertWorkspaceSetting()<br/>ListWorkspaceSettings()"]
        ATT_OPS["CreateAttachment()<br/>ListAttachments()"]
    end

    SDI --> MEMO_OPS
    SDI --> USER_OPS
    SDI --> WS_OPS
    SDI --> ATT_OPS

    %% Database Drivers
    subgraph DD["Database Drivers"]
        SQLITE["sqlite.DB"]
        MYSQL["mysql.DB"]
        POSTGRES["postgres.DB"]
    end

    MEMO_OPS --> SQLITE
    MEMO_OPS --> MYSQL
    MEMO_OPS --> POSTGRES
    USER_OPS --> SQLITE
    USER_OPS --> MYSQL
    USER_OPS --> POSTGRES
    WS_OPS --> SQLITE
    WS_OPS --> MYSQL
    WS_OPS --> POSTGRES
    ATT_OPS --> SQLITE
    ATT_OPS --> MYSQL
    ATT_OPS --> POSTGRES

    %% Physical Storage
    subgraph PS["Physical Storage"]
        SQLITEDB["SQLite Database<br/>*.db files"]
        MYSQLDB["MySQL Database"]
        POSTGRESDB["PostgreSQL Database"]
    end

    SQLITE --> SQLITEDB
    MYSQL --> MYSQLDB
    POSTGRES --> POSTGRESDB

    %% Styling
    classDef storeAbstraction fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef cacheLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef driverInterface fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef databaseDrivers fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef physicalStorage fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class SA,SS storeAbstraction
    class CL,WSC,UC,USC cacheLayer
    class SDI,DIM,MEMO_OPS,USER_OPS,WS_OPS,ATT_OPS driverInterface
    class DD,SQLITE,MYSQL,POSTGRES databaseDrivers
    class PS,SQLITEDB,MYSQLDB,POSTGRESDB physicalStorage
```

存储架构提供：

- **统一接口**：所有数据库操作通过 `store.Driver` 方法
- **多数据库支持**：SQLite、MySQL 和 PostgreSQL 驱动实现相同接口
- **缓存层**：具有 TTL 的内存缓存，用于频繁访问的数据
- **迁移系统**：`store/migration/` 中的版本控制模式迁移

## API 服务层

API 层实现处理业务逻辑和数据操作的 gRPC 服务。每个服务专注于具有明确边界的特定域。

**来源：** [server/router/api/v1/markdown_service.go 17-55](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/markdown_service.go#L17-L55) [proto/api/v1/markdown_service.proto 10-43](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/markdown_service.proto#L10-L43) [proto/gen/api/v1/markdown_service.pb.go 25-134](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/markdown_service.pb.go#L25-L134)

```mermaid
flowchart TD
    %% gRPC Service Registration
    subgraph GSR["gRPC Service Registration"]
        API1S["APIV1Service Struct"]
    end

    %% Core Services
    subgraph CS["Core Services"]
        WSM["WorkspaceService Methods<br/>GetWorkspaceProfile()<br/>UpdateWorkspaceProfile()<br/>GetWorkspaceSetting()"]
        MSM["MarkdownService Methods<br/>ParseMarkdown()<br/>RestoreMarkdownNodes()<br/>StringifyMarkdownNodes()<br/>GetLinkMetadata()"]
        ASM["AuthService Methods<br/>GetAuthStatus()<br/>SignIn()<br/>SignUp()<br/>SignOut()"]
        USM["UserService Methods<br/>GetUser()<br/>CreateUser()<br/>UpdateUser()<br/>ListUsers()"]
        MEMO_SM["MemoService Methods<br/>CreateMemo()<br/>GetMemo()<br/>ListMemos()<br/>UpdateMemo()<br/>DeleteMemo()"]
    end

    API1S --> WSM
    API1S --> MSM
    API1S --> ASM
    API1S --> USM
    API1S --> MEMO_SM

    %% Protocol Buffer Definitions
    subgraph PBD["Protocol Buffer Definitions"]
        API_PROTO["api/v1/*.proto<br/>Request/Response Messages"]
        STORE_PROTO["store/*.proto<br/>Data Model Messages"]
    end

    API1S --> API_PROTO
    API1S --> STORE_PROTO

    %% Generated Code
    subgraph GC["Generated Code"]
        PB_GO["*.pb.go<br/>Generated gRPC Stubs"]
        PB_GW["*.pb.gw.go<br/>Generated Gateway Code"]
    end

    API1S --> PB_GO
    API1S --> PB_GW

    %% Registration endpoints
    API1S --> GSReg["grpc.Server Registration"]
    API1S --> GWReg["Gateway Registration<br/>HTTP/JSON Endpoints"]

    %% Protocol Buffer to Generated Code connections
    API_PROTO --> PB_GO
    API_PROTO --> PB_GW
    STORE_PROTO --> PB_GO

    %% Styling
    classDef serviceRegistration fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef coreServices fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef protocolBuffers fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef generatedCode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef registration fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class GSR,API1S serviceRegistration
    class CS,WSM,MSM,ASM,USM,MEMO_SM coreServices
    class PBD,API_PROTO,STORE_PROTO protocolBuffers
    class GC,PB_GO,PB_GW generatedCode
    class GSReg,GWReg registration
```

服务职责：

- **MemoService**：备忘录、标签和关系的 CRUD 操作
- **UserService**：用户账户管理和偏好设置
- **AuthService**：身份验证、会话管理和访问令牌
- **MarkdownService**：Markdown 解析、渲染和链接元数据提取
- **WorkspaceService**：工作空间级别的设置和配置

## 数据流和请求处理

以下图表说明了请求如何从前端流向数据库，显示了完整的请求生命周期。

**来源：** [server/server.go 87-91](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L87-L91) [store/store.go 24-44](https://github.com/usememos/memos/blob/50a41a39/store/store.go#L24-L44) [web/src/main.tsx 23-25](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L23-L25)

```mermaid
sequenceDiagram
    participant Frontend as React Frontend
    participant GRPCWeb as gRPC-Web Client
    participant Gateway as gRPC-Gateway
    participant Auth as AuthInterceptor
    participant Service as APIV1Service
    participant Store as store.Store
    participant Cache as cache.Cache
    participant Driver as store.Driver
    participant DB as Database

    Note over Frontend,DB: User Action (e.g., Create Memo)

    Frontend->>GRPCWeb: User Action (e.g., Create Memo)
    GRPCWeb->>Gateway: HTTP POST /api/v1/memos
    Gateway->>Auth: gRPC CreateMemo Request
    Auth->>Auth: Validate JWT Token

    Note over Auth: Authenticated Request
    Auth->>Service: Authenticated Request
    Service->>Store: CreateMemo(ctx, memo)
    Store->>Cache: Check Cache
    Cache-->>Store: Cache Miss
    Store->>Driver: CreateMemo(ctx, create)
    Driver->>DB: SQL INSERT
    DB-->>Driver: Memo Record
    Driver-->>Store: Memo Model
    Store->>Cache: Set Cache Entry
    Store-->>Service: Created Memo
    Service-->>Gateway: gRPC Response
    Gateway-->>GRPCWeb: HTTP JSON Response
    GRPCWeb-->>Frontend: Memo Created

    Note over Frontend,DB: Request Complete
```

请求处理层：

- **前端层**：React 组件通过 MobX 存储触发操作
- **传输层**：gRPC-Web 转换为 HTTP 请求
- **网关层**：gRPC-Gateway 将 HTTP 转换为 gRPC 调用
- **身份验证层**：JWT 验证和会话管理
- **服务层**：业务逻辑和数据验证
- **存储层**：缓存和数据库操作
- **数据库层**：持久数据存储
