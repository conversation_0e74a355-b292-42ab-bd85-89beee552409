{"theme": "<PERSON><PERSON><PERSON>", "selectedAuthType": "oauth-personal", "preferredEditor": "vscode", "mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.3"], "env": {"TAVILY_API_KEY": "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"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "/mnt/d/xianjinhui/Desktop/create/memos/shrimp-task-manager/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "true"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}}, "checkpointing": {"enabled": true}}