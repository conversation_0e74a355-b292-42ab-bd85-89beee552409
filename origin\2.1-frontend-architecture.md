# 前端架构

> 📅 **最后索引**: 2025年6月30日 ([50a41a](https://github.com/usememos/memos/commits/50a41a39))

<details>
<summary><strong>🔍 点击展开查看核心源文件列表</strong></summary>

**核心前端文件:**

- [web/src/main.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx) - 应用程序入口点
- [web/src/App.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/App.tsx) - 根组件和主题管理
- [web/src/router/index.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/router/index.tsx) - 路由配置
- [web/package.json](https://github.com/usememos/memos/blob/50a41a39/web/package.json) - 依赖和脚本配置
- [web/vite.config.ts](https://github.com/usememos/memos/blob/50a41a39/web/vite.config.ts) - 构建配置

**状态管理:**

- [web/src/store/v2/](https://github.com/usememos/memos/tree/50a41a39/web/src/store/v2) - MobX 存储层
- [web/src/components/Navigation.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/components/Navigation.tsx) - 导航组件
- [web/src/components/UserBanner.tsx](https://github.com/usememos/memos/blob/50a41a39/web/src/components/UserBanner.tsx) - 用户横幅

**UI 组件:**

- [web/src/components/MemoContent/](https://github.com/usememos/memos/tree/50a41a39/web/src/components/MemoContent) - 内容渲染组件
- [web/src/components/HomeSidebar/](https://github.com/usememos/memos/tree/50a41a39/web/src/components/HomeSidebar) - 侧边栏组件

</details>

---

## 概述

本文档详细介绍了 Memos 前端应用程序的结构和组织。它涵盖了技术栈、应用程序结构、状态管理方法、组件架构和其他重要的前端方面。有关后端架构的信息，请参阅 [后端架构](./2.2-backend-architecture.md)。

## 技术栈

Memos 使用基于现代 React 的前端技术栈，包含以下关键技术：

| 类别 | 技术 | 版本 |
|------|------|------|
| 核心框架 | React, TypeScript | 18.3.1, 5.8.3 |
| 构建工具 | Vite, ESBuild | 6.3.5 |
| 路由 | react-router-dom | 7.6.1 |
| 状态管理 | MobX, mobx-react-lite | 6.13.7, 4.1.0 |
| UI 组件 | @mui/joy, @usememos/mui | 5.0.0-beta.51, 0.1.0 |
| 样式 | tailwindcss, @emotion/react | 3.4.17, 11.14.0 |
| API 通信 | nice-grpc-web, @bufbuild/protobuf | 3.3.7, 2.5.1 |
| 国际化 | i18next, react-i18next | 25.2.1, 15.5.2 |
| 内容渲染 | katex, mermaid, highlight.js | 0.16.22, 11.6.0, 11.11.1 |
| 地图和可视化 | leaflet, react-leaflet, react-force-graph-2d | 1.9.4, 4.2.1, 1.27.1 |
| 工具库 | dayjs, lodash-es, fuse.js | 1.11.13, 4.17.21, 7.1.0 |

**来源：** [web/package.json 10-84](https://github.com/usememos/memos/blob/50a41a39/web/package.json#L10-L84)

## MobX 状态管理

Memos 在整个应用程序中使用 MobX 进行响应式状态管理。所有存储都位于 `/store/v2` 中，并遵循观察者模式进行自动组件重新渲染。

### 存储架构

**来源：** [web/src/App.tsx 8-16](https://github.com/usememos/memos/blob/50a41a39/web/src/App.tsx#L8-L16) [web/src/main.tsx 10-11](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L10-L11) [web/src/components/Navigation.tsx 8-36](https://github.com/usememos/memos/blob/50a41a39/web/src/components/Navigation.tsx#L8-L36) [web/src/components/HomeSidebar/HomeSidebar.tsx 8-37](https://github.com/usememos/memos/blob/50a41a39/web/src/components/HomeSidebar/HomeSidebar.tsx#L8-L37) [web/src/components/HomeSidebar/ShortcutsSection.tsx 8-46](https://github.com/usememos/memos/blob/50a41a39/web/src/components/HomeSidebar/ShortcutsSection.tsx#L8-L46)

```mermaid
flowchart TD
    %% MobX Store Layer (/store/v2)
    subgraph MSL["MobX Store Layer (/store/v2)"]
        US["userStore"]
        WS["workspaceStore"]
        MS["memoStore"]
        MFS["memoFilterStore"]
    end

    %% Store Initialization
    subgraph SI["Store Initialization"]
        IUS["initialUserStore()"]
        IWS["initialWorkspaceStore()"]
    end

    IUS --> US
    IWS --> WS

    %% Observer Components
    subgraph OC["Observer Components"]
        APP_OBS["App (observer)"]
        NAV_OBS["Navigation (observer)"]
        HSB_OBS["HomeSidebar (observer)"]
        SV_OBS["StatisticsView (observer)"]
        SS_OBS["ShortcutsSection (observer)"]
    end

    US --> APP_OBS
    WS --> NAV_OBS
    MS --> HSB_OBS
    MFS --> SV_OBS
    US --> SS_OBS

    %% State Properties
    subgraph SP["State Properties"]
        USER_SETTING["userStore.state.userSetting"]
        WS_PROFILE["workspaceStore.state.profile"]
        MEMO_STATE["memoStore.state.memos"]
        FILTER_SHORTCUT["memoFilterStore.shortcut"]
    end

    US --> USER_SETTING
    WS --> WS_PROFILE
    MS --> MEMO_STATE
    MFS --> FILTER_SHORTCUT

    %% Styling
    classDef store fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef init fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef observer fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef state fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000

    class MSL,US,WS,MS,MFS store
    class SI,IUS,IWS init
    class OC,APP_OBS,NAV_OBS,HSB_OBS,SV_OBS,SS_OBS observer
    class SP,USER_SETTING,WS_PROFILE,MEMO_STATE,FILTER_SHORTCUT state
```

关键的 MobX 存储包括：

- **`userStore`**: 用户身份验证、偏好设置、快捷方式和统计信息，通过 `fetchUserStats()`、`fetchShortcuts()` 获取
- **`workspaceStore`**: 工作空间配置、常规设置、语言环境和外观
- **`memoStore`**: 备忘录数据收集和 CRUD 操作
- **`memoFilterStore`**: 过滤状态，包含 `setShortcut()`、`addFilter()`、`removeFilter()` 等方法

## 应用程序架构概览

前端遵循分层架构，从应用程序入口点到组件层次有明确的分离。系统围绕单个 React 应用程序构建，该应用程序使用路由器和状态管理来协调用户界面。

### 前端应用程序结构

**来源：** [web/src/main.tsx 1-31](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L1-L31) [web/src/App.tsx 1-111](https://github.com/usememos/memos/blob/50a41a39/web/src/App.tsx#L1-L111) [web/src/router/index.tsx 1-185](https://github.com/usememos/memos/blob/50a41a39/web/src/router/index.tsx#L1-L185)

```mermaid
flowchart TD
    %% Entry Point
    MAIN["main.tsx<br/>Application Entry"]

    %% Store Initialization
    MAIN --> IWS["initialWorkspaceStore()"]
    MAIN --> IUS["initialUserStore()"]

    %% Core Application
    MAIN --> APP["App.tsx<br/>(observer)"]

    %% Providers
    APP --> CVP["CssVarsProvider<br/>(@mui/joy)"]
    APP --> RP["RouterProvider<br/>(React Router)"]

    %% Router Configuration
    RP --> ROUTER["createBrowserRouter<br/>Route Configuration"]

    %% Layout Hierarchy
    ROUTER --> RL["RootLayout"]
    RL --> HL["HomeLayout"]
    HL --> OUTLET["Outlet<br/>(Route Content)"]

    %% Page Components (Lazy Loaded)
    OUTLET --> HOME["Home"]
    OUTLET --> EXPLORE["Explore (lazy)"]
    OUTLET --> SETTING["Setting (lazy)"]
    OUTLET --> SIGNIN["SignIn (lazy)"]
    OUTLET --> MEMO_DETAIL["MemoDetail (lazy)"]
    OUTLET --> USER_PROFILE["UserProfile (lazy)"]

    %% Store Integration
    IWS --> WS["workspaceStore"]
    IUS --> US["userStore"]

    %% Additional Stores
    US --> MS["memoStore"]
    US --> MFS["memoFilterStore"]

    %% Component Integration
    HOME --> NAV["Navigation (observer)"]
    HOME --> HSB["HomeSidebar (observer)"]
    HOME --> UB["UserBanner"]

    %% Styling
    classDef entry fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef core fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef providers fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef layout fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef pages fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef stores fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000
    classDef components fill:#f1f8e9,stroke:#33691e,stroke-width:2px,color:#000000

    class MAIN entry
    class APP core
    class CVP,RP providers
    class ROUTER,RL,HL,OUTLET layout
    class HOME,EXPLORE,SETTING,SIGNIN,MEMO_DETAIL,USER_PROFILE pages
    class IWS,IUS,WS,US,MS,MFS stores
    class NAV,HSB,UB components
```

## 应用程序初始化和引导

应用程序初始化流程遵循以下步骤：

1. `main.tsx` 作为入口点
2. 初始化工作空间和用户存储
3. 使用路由器和主题提供程序渲染 React 应用程序
4. `App` 组件处理全局设置和主题管理

**来源：** [web/src/main.tsx 1-31](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L1-L31) [web/src/App.tsx 1-115](https://github.com/usememos/memos/blob/50a41a39/web/src/App.tsx#L1-L115)

```mermaid
sequenceDiagram
    participant MAIN as main.tsx
    participant STORES as Store Initialization
    participant APP as App.tsx
    participant PROVIDERS as Providers
    participant ROUTER as Router
    participant COMPONENTS as Components

    Note over MAIN,COMPONENTS: 应用程序初始化流程

    MAIN->>STORES: initialWorkspaceStore()
    MAIN->>STORES: initialUserStore()
    STORES-->>MAIN: 存储初始化完成

    MAIN->>APP: Render App Component
    APP->>PROVIDERS: Setup CssVarsProvider
    APP->>PROVIDERS: Setup RouterProvider
    PROVIDERS->>APP: 主题和路由配置完成

    APP->>APP: 处理工作空间设置
    APP->>APP: 管理语言和主题

    PROVIDERS->>ROUTER: 激活路由系统
    ROUTER->>COMPONENTS: 根据 URL 渲染页面组件

    Note over MAIN,COMPONENTS: 应用程序就绪
```

## 路由系统

Memos 使用 React Router v7 和分层路由结构。路由系统定义了几个关键布局和嵌套路由。

**来源：** [web/src/router/index.tsx 1-185](https://github.com/usememos/memos/blob/50a41a39/web/src/router/index.tsx#L1-L185)

```mermaid
flowchart TD
    %% Router Root
    CBR["createBrowserRouter"]

    %% Root Layout
    CBR --> RL["RootLayout<br/>(/)"]

    %% Main Application Routes (under HomeLayout)
    RL --> HL["HomeLayout"]

    subgraph MAIN_ROUTES["Main Application Routes"]
        HOME_R["/"]
        EXPLORE_R["/explore"]
        ARCHIVED_R["/archived"]
        RESOURCES_R["/resources"]
        INBOX_R["/inbox"]
        SETTING_R["/setting/*"]
        MEMO_DETAIL_R["/memos/:uid"]
        USER_PROFILE_R["/u/:username"]
    end

    HL --> HOME_R
    HL --> EXPLORE_R
    HL --> ARCHIVED_R
    HL --> RESOURCES_R
    HL --> INBOX_R
    HL --> SETTING_R
    HL --> MEMO_DETAIL_R
    HL --> USER_PROFILE_R

    %% Auth Routes (direct under RootLayout)
    subgraph AUTH_ROUTES["Authentication Routes"]
        AUTH_ROOT["/auth"]
        AUTH_SIGNUP["/auth/signup"]
        AUTH_ADMIN["/auth/admin"]
        AUTH_CALLBACK["/auth/callback"]
    end

    RL --> AUTH_ROOT
    RL --> AUTH_SIGNUP
    RL --> AUTH_ADMIN
    RL --> AUTH_CALLBACK

    %% Lazy Loading
    subgraph LAZY["Lazy Loaded Components"]
        EXPLORE_LAZY["Explore (lazy)"]
        SETTING_LAZY["Setting (lazy)"]
        SIGNIN_LAZY["SignIn (lazy)"]
        MEMO_DETAIL_LAZY["MemoDetail (lazy)"]
        USER_PROFILE_LAZY["UserProfile (lazy)"]
    end

    EXPLORE_R --> EXPLORE_LAZY
    SETTING_R --> SETTING_LAZY
    AUTH_ROOT --> SIGNIN_LAZY
    MEMO_DETAIL_R --> MEMO_DETAIL_LAZY
    USER_PROFILE_R --> USER_PROFILE_LAZY

    %% Styling
    classDef router fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef layout fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef mainRoutes fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef authRoutes fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef lazy fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class CBR,RL router
    class HL layout
    class MAIN_ROUTES,HOME_R,EXPLORE_R,ARCHIVED_R,RESOURCES_R,INBOX_R,SETTING_R,MEMO_DETAIL_R,USER_PROFILE_R mainRoutes
    class AUTH_ROUTES,AUTH_ROOT,AUTH_SIGNUP,AUTH_ADMIN,AUTH_CALLBACK authRoutes
    class LAZY,EXPLORE_LAZY,SETTING_LAZY,SIGNIN_LAZY,MEMO_DETAIL_LAZY,USER_PROFILE_LAZY lazy
```

大多数页面组件使用 React 的 `lazy()` 函数加载，这启用了代码分割并提高了初始加载性能。

## 组件架构

前端使用按功能和可重用性组织的分层组件架构：

**来源：** [web/src/router/index.tsx 24-32](https://github.com/usememos/memos/blob/50a41a39/web/src/router/index.tsx#L24-L32) [web/src/components/Navigation.tsx 39-64](https://github.com/usememos/memos/blob/50a41a39/web/src/components/Navigation.tsx#L39-L64) [web/src/components/UserBanner.tsx 15-72](https://github.com/usememos/memos/blob/50a41a39/web/src/components/UserBanner.tsx#L15-L72)

```mermaid
flowchart TD
    %% Layout Components
    subgraph LC["Layout Components"]
        RL["RootLayout"]
        HL["HomeLayout"]
    end

    %% Page Components
    subgraph PC["Page Components"]
        HOME["Home"]
        EXPLORE["Explore"]
        SETTING["Setting"]
        MEMO_DETAIL["MemoDetail"]
        USER_PROFILE["UserProfile"]
    end

    RL --> HL
    HL --> HOME
    HL --> EXPLORE
    HL --> SETTING
    HL --> MEMO_DETAIL
    HL --> USER_PROFILE

    %% Navigation Components
    subgraph NC["Navigation Components"]
        NAV["Navigation (observer)"]
        UB["UserBanner"]
        MB_HEADER["MobileHeader"]
        NAV_DRAWER["NavigationDrawer"]
    end

    HOME --> NAV
    HOME --> UB
    HOME --> MB_HEADER
    NAV --> NAV_DRAWER

    %% Sidebar Components
    subgraph SC["Sidebar Components"]
        HSB["HomeSidebar (observer)"]
        SS["ShortcutsSection (observer)"]
        TS["TagsSection"]
        SV["StatisticsView (observer)"]
        MF["MemoFilters"]
    end

    HOME --> HSB
    HSB --> SS
    HSB --> TS
    HSB --> SV
    HSB --> MF

    %% Content Components
    subgraph CC["Content Components"]
        ME["MemoEditor"]
        MC["MemoContent"]
        CB["CodeBlock"]
        MB["MermaidBlock"]
        ML["MemoList"]
    end

    HOME --> ME
    HOME --> MC
    HOME --> ML
    MC --> CB
    MC --> MB

    %% UI Components
    subgraph UC["UI Components"]
        UA["UserAvatar"]
        AC["ActivityCalendar (observer)"]
        ICON["Icon"]
        TOOLTIP["Tooltip"]
    end

    SV --> UA
    SV --> AC
    NAV --> ICON
    UB --> TOOLTIP

    %% Styling
    classDef layout fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef page fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef navigation fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef sidebar fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef content fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef ui fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000

    class LC,RL,HL layout
    class PC,HOME,EXPLORE,SETTING,MEMO_DETAIL,USER_PROFILE page
    class NC,NAV,UB,MB_HEADER,NAV_DRAWER navigation
    class SC,HSB,SS,TS,SV,MF sidebar
    class CC,ME,MC,CB,MB,ML content
    class UC,UA,AC,ICON,TOOLTIP ui
```

### 关键组件类别

**布局组件**: 定义页面结构

- `RootLayout`: 带路由的主应用程序容器
- `HomeLayout`: 专门用于备忘录焦点页面的布局

**导航组件**: 处理应用程序导航

- `Navigation`: 主导航，包含路由（`Routes.ROOT`、`Routes.EXPLORE`、`Routes.RESOURCES`）
- `UserBanner`: 用户下拉菜单，包含个人资料、设置和退出操作
- `MobileHeader`: 移动端专用头部组件
- `NavigationDrawer`: 移动端导航抽屉

**侧边栏组件**: 提供过滤和统计功能

- `HomeSidebar`: 所有侧边栏功能的容器
- `StatisticsView`: 活动日历和备忘录统计
- `ShortcutsSection`: 用户定义的快捷方式，包含 `showCreateShortcutDialog()`
- `TagsSection`: 标签管理和过滤
- `MemoFilters`: 备忘录过滤器

**内容组件**: 渲染备忘录内容

- `MemoEditor`: 备忘录编辑器
- `MemoContent`: 备忘录内容渲染器
- `CodeBlock`: 语法高亮，使用 `hljs.highlight()` 和 `SpecialLanguage.MERMAID` 的特殊处理
- `MermaidBlock`: Mermaid 图表渲染
- `MemoList`: 备忘录列表组件

## 响应式设计

应用程序使用响应式设计方法：

- Tailwind CSS 实用程序类用于响应式样式
- 组件特定的响应式行为
- 专用移动组件（如 `MobileHeader`）
- 小屏幕的移动导航抽屉

**来源：** [web/src/components/MobileHeader.tsx 1-31](https://github.com/usememos/memos/blob/50a41a39/web/src/components/MobileHeader.tsx#L1-L31) [web/src/components/NavigationDrawer.tsx 1-47](https://github.com/usememos/memos/blob/50a41a39/web/src/components/NavigationDrawer.tsx#L1-L47)

```mermaid
flowchart TD
    %% Screen Sizes
    subgraph SS["Screen Sizes"]
        MOBILE["Mobile"]
        TABLET["Tablet"]
        DESKTOP["Desktop"]
    end

    %% Responsive Behavior
    subgraph RB["Responsive Behavior"]
        URW["useResponsiveWidth Hook"]
        TB["Tailwind Breakpoints"]
        MSC["Mobile-specific Components"]
        DC["Drawer Components"]
    end

    MOBILE --> URW
    TABLET --> TB
    DESKTOP --> MSC

    URW --> DC
    TB --> DC
    MSC --> DC

    %% Styling
    classDef screen fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef responsive fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000

    class SS,MOBILE,TABLET,DESKTOP screen
    class RB,URW,TB,MSC,DC responsive
```

应用程序检测屏幕大小并相应地调整 UI。

## 资源加载和代码分割

Memos 使用多种技术来优化加载性能：

- **代码分割**: React.lazy 用于组件懒加载
- **块管理**: Vite 配置为将供应商依赖项分割为单独的块
- **Suspense**: React Suspense 在组件加载期间提供加载状态

**来源：** [web/src/router/index.tsx 1-185](https://github.com/usememos/memos/blob/50a41a39/web/src/router/index.tsx#L1-L185) [web/vite.config.ts 42-64](https://github.com/usememos/memos/blob/50a41a39/web/vite.config.ts#L42-L64)

```mermaid
flowchart TD
    %% Loading Optimizations
    subgraph LO["Loading Optimizations"]
        LAZY["React.lazy()"]
        SUSPENSE["React.Suspense"]
        VCC["Vite Chunk Configuration"]
    end

    %% Resources
    subgraph R["Resources"]
        PC["Page Components"]
        VL["Vendor Libraries"]
        UE["User Experience"]
    end

    LAZY --> PC
    SUSPENSE --> VL
    VCC --> UE

    PC --> LOD["loaded on demand"]
    VL --> OC["optimized caching"]
    UE --> OC

    %% Styling
    classDef optimization fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef resource fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef result fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000

    class LO,LAZY,SUSPENSE,VCC optimization
    class R,PC,VL,UE resource
    class LOD,OC result
```

`vite.config.ts` 文件为关键依赖项定义手动块：

- react-vendor: React 核心库
- mui-vendor: MUI 组件
- utils-vendor: 实用程序库
- 专门的内容渲染库（KaTeX、highlight.js、mermaid）

## 构建和开发环境

Vite 提供构建系统，支持 React、TypeScript 和传统浏览器。

**来源：** [web/package.json 4-8](https://github.com/usememos/memos/blob/50a41a39/web/package.json#L4-L8) [web/vite.config.ts 14-65](https://github.com/usememos/memos/blob/50a41a39/web/vite.config.ts#L14-L65)

```mermaid
flowchart TD
    %% Vite Configuration
    subgraph VC["Vite Configuration"]
        VITE_CONFIG["vite.config.ts"]
        REACT_PLUGIN["@vitejs/plugin-react"]
        LEGACY_PLUGIN["@vitejs/plugin-legacy"]
        CODE_INSPECTOR["code-inspector-plugin"]
    end

    VITE_CONFIG --> REACT_PLUGIN
    VITE_CONFIG --> LEGACY_PLUGIN
    VITE_CONFIG --> CODE_INSPECTOR

    %% Development Server
    subgraph DS["Development Server"]
        DEV_SERVER["host: '0.0.0.0', port: 3001"]
        PROXY["proxy: API, gRPC, file routes"]
        HMR["Hot Module Replacement"]
    end

    VITE_CONFIG --> DEV_SERVER
    DEV_SERVER --> PROXY
    DEV_SERVER --> HMR

    %% Build Output
    subgraph BO["Build Output"]
        ASSETS["assets/app.[hash].js"]
        MANUAL_CHUNKS["Manual Chunks"]
    end

    VITE_CONFIG --> ASSETS
    VITE_CONFIG --> MANUAL_CHUNKS

    %% Manual Chunks
    subgraph MC["Manual Chunks (Vendor Splitting)"]
        REACT_VENDOR["'react-vendor': React, ReactDOM, Router"]
        MUI_VENDOR["'mui-vendor': MUI Joy, Emotion"]
        UTILS_VENDOR["'utils-vendor': dayjs, lodash, MobX"]
        KATEX_VENDOR["katex-vendor, highlight-vendor, mermaid-vendor"]
        LEAFLET_VENDOR["'leaflet-vendor': Leaflet, React Leaflet"]
    end

    MANUAL_CHUNKS --> REACT_VENDOR
    MANUAL_CHUNKS --> MUI_VENDOR
    MANUAL_CHUNKS --> UTILS_VENDOR
    MANUAL_CHUNKS --> KATEX_VENDOR
    MANUAL_CHUNKS --> LEAFLET_VENDOR

    %% Package Scripts
    subgraph PS["Package Scripts"]
        DEV_SCRIPT["pnpm dev → vite"]
        BUILD_SCRIPT["pnpm build → vite build"]
        RELEASE_SCRIPT["pnpm release → vite build --mode release --outDir=../server/router/frontend/dist"]
        LINT_SCRIPT["pnpm lint → tsc + eslint"]
    end

    VITE_CONFIG --> DEV_SCRIPT
    VITE_CONFIG --> BUILD_SCRIPT
    VITE_CONFIG --> RELEASE_SCRIPT
    VITE_CONFIG --> LINT_SCRIPT

    %% Styling
    classDef config fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef dev fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef build fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef chunks fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef scripts fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class VC,VITE_CONFIG,REACT_PLUGIN,LEGACY_PLUGIN,CODE_INSPECTOR config
    class DS,DEV_SERVER,PROXY,HMR dev
    class BO,ASSETS,MANUAL_CHUNKS build
    class MC,REACT_VENDOR,MUI_VENDOR,UTILS_VENDOR,KATEX_VENDOR,LEAFLET_VENDOR chunks
    class PS,DEV_SCRIPT,BUILD_SCRIPT,RELEASE_SCRIPT,LINT_SCRIPT scripts
```

### 关键配置特性

**开发特性**:

- 开发服务器在 `0.0.0.0:3001` 上运行，支持 HMR
- 后端 API 路由的代理配置
- 用于调试的代码检查器插件

**生产优化**:

- 手动块分割以实现最佳缓存
- 传统浏览器支持，目标为 `["defaults", "not IE 11"]`
- 缓存清除的哈希文件名：`assets/app.[hash].js`

**发布构建**: `release` 脚本直接构建到 `../server/router/frontend/dist` 以进行后端集成。

## 前后端集成

前端通过使用 `nice-grpc-web` 和 protobuf 定义的 gRPC-Web 与后端通信。

**来源：** [web/src/components/UserBanner.tsx 3-23](https://github.com/usememos/memos/blob/50a41a39/web/src/components/UserBanner.tsx#L3-L23) [web/src/components/HomeSidebar/ShortcutsSection.tsx 4-28](https://github.com/usememos/memos/blob/50a41a39/web/src/components/HomeSidebar/ShortcutsSection.tsx#L4-L28) [web/vite.config.ts 7-41](https://github.com/usememos/memos/blob/50a41a39/web/vite.config.ts#L7-L41)

```mermaid
flowchart TD
    %% Frontend Components
    subgraph FC["Frontend Components"]
        UB["UserBanner"]
        SS["ShortcutsSection"]
        OTHER["Other Components"]
    end

    %% gRPC Client Layer
    subgraph GCL["gRPC Client Layer"]
        ASC["authServiceClient"]
        SSC["shortcutServiceClient"]
        GRPC_WEB["@/grpcweb"]
    end

    UB --> ASC
    SS --> SSC
    OTHER --> GRPC_WEB

    ASC --> GRPC_WEB
    SSC --> GRPC_WEB

    %% Development Proxy
    subgraph DP["Development Proxy (Vite)"]
        API_PROXY["'^/api' → devProxyServer"]
        GRPC_PROXY["'^/memos.api.v1' → devProxyServer"]
        FILE_PROXY["'^/file' → devProxyServer"]
    end

    GRPC_WEB --> API_PROXY
    GRPC_WEB --> GRPC_PROXY
    GRPC_WEB --> FILE_PROXY

    %% Backend Services
    subgraph BS["Backend Services"]
        AUTH_SIGNOUT["AuthService.signOut()"]
        SHORTCUT_DELETE["ShortcutService.deleteShortcut()"]
        FILE_UPLOAD["File Upload/Download"]
        REST_API["REST API Endpoints"]
    end

    API_PROXY --> AUTH_SIGNOUT
    GRPC_PROXY --> SHORTCUT_DELETE
    FILE_PROXY --> FILE_UPLOAD
    API_PROXY --> REST_API

    %% Protocol Buffers
    subgraph PB["Protocol Buffers"]
        BUFBUILD["@bufbuild/protobuf"]
        TYPE_SAFE["Type-safe Service Definitions"]
    end

    GRPC_WEB --> BUFBUILD
    BUFBUILD --> TYPE_SAFE

    %% Styling
    classDef frontend fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef grpc fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef proxy fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef backend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef protobuf fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000

    class FC,UB,SS,OTHER frontend
    class GCL,ASC,SSC,GRPC_WEB grpc
    class DP,API_PROXY,GRPC_PROXY,FILE_PROXY proxy
    class BS,AUTH_SIGNOUT,SHORTCUT_DELETE,FILE_UPLOAD,REST_API backend
    class PB,BUFBUILD,TYPE_SAFE protobuf
```

### gRPC 服务集成

**服务客户端使用示例**:

- `authServiceClient.signOut({})` - 用户身份验证
- `shortcutServiceClient.deleteShortcut({ parent: user.name, id: shortcut.id })` - 快捷方式管理

**开发代理配置**:

```javascript
proxy: {
  "^/api": { target: devProxyServer, xfwd: true },
  "^/memos.api.v1": { target: devProxyServer, xfwd: true },
  "^/file": { target: devProxyServer, xfwd: true }
}
```

`devProxyServer` 默认为 `http://localhost:8081`，可通过 `DEV_PROXY_SERVER` 环境变量覆盖。

## 样式和主题

Memos 使用以下组合：

- **Tailwind CSS**: 用于基于实用程序的样式
- **MUI Joy**: 用于组件样式和主题
- **CSS 模块**: 用于组件特定样式
- **自定义主题配置**: 用于一致的设计语言

应用程序支持明暗模式，自动检测系统偏好。主题由工作空间存储管理，并通过 MUI 的主题提供程序应用。

**来源：** [web/src/App.tsx 33-98](https://github.com/usememos/memos/blob/50a41a39/web/src/App.tsx#L33-L98) [web/src/main.tsx 16-21](https://github.com/usememos/memos/blob/50a41a39/web/src/main.tsx#L16-L21)

## 总结

Memos 前端架构遵循现代 React 模式，专注于组件可重用性、高效状态管理和响应式设计。双状态管理方法（MobX 和 Zustand）允许在管理不同类型的应用程序状态时具有灵活性，而代码分割和懒加载的使用则实现了高效的加载性能。
