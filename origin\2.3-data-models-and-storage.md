# 数据模型和存储

> 📅 **最后索引**: 2025年6月30日 ([50a41a](https://github.com/usememos/memos/commits/50a41a39))

<details>
<summary><strong>🔍 点击展开查看核心源文件列表</strong></summary>

**存储层文件:**

- [store/store.go](https://github.com/usememos/memos/blob/50a41a39/store/store.go) - 存储抽象层
- [store/driver.go](https://github.com/usememos/memos/blob/50a41a39/store/driver.go) - 驱动程序接口
- [store/migrator.go](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go) - 数据库迁移系统

**数据模型:**

- [store/memo.go](https://github.com/usememos/memos/blob/50a41a39/store/memo.go) - 备忘录数据模型
- [store/user.go](https://github.com/usememos/memos/blob/50a41a39/store/user.go) - 用户数据模型
- [store/workspace_setting.go](https://github.com/usememos/memos/blob/50a41a39/store/workspace_setting.go) - 工作空间设置
- [store/user_setting.go](https://github.com/usememos/memos/blob/50a41a39/store/user_setting.go) - 用户设置

**数据库驱动:**

- [store/db/sqlite/](https://github.com/usememos/memos/tree/50a41a39/store/db/sqlite) - SQLite 驱动实现
- [store/db/mysql/](https://github.com/usememos/memos/tree/50a41a39/store/db/mysql) - MySQL 驱动实现
- [store/db/postgres/](https://github.com/usememos/memos/tree/50a41a39/store/db/postgres) - PostgreSQL 驱动实现

**查询过滤:**

- [plugin/filter/](https://github.com/usememos/memos/tree/50a41a39/plugin/filter) - CEL 查询过滤系统

</details>

---

## 概述

本文档涵盖了 Memos 应用程序的数据持久化层，包括数据模型、存储抽象、数据库访问模式、缓存机制和查询过滤。有关后端服务架构的信息，请参阅 [后端架构](./2.2-backend-architecture.md)。有关数据库部署配置，请参阅 [数据存储层](../4.7-data-storage-layer.md)。

## 存储架构概览

Memos 应用程序使用分层存储架构，通过统一接口抽象数据库操作，同时支持多个数据库后端。

### 存储层架构

**来源：** [store/store.go 10-57](https://github.com/usememos/memos/blob/50a41a39/store/store.go#L10-L57) [store/memo.go 114-147](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L114-L147) [store/user.go 94-159](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L94-L159) [store/driver.go 12-79](https://github.com/usememos/memos/blob/50a41a39/store/driver.go#L12-L79)

```mermaid
flowchart TD
    %% Service Layer
    subgraph SL["Service Layer"]
        MEMO_SVC["MemoService"]
        USER_SVC["UserService"]
        WS_SVC["WorkspaceService"]
    end
    
    %% Store Abstraction
    subgraph SA["Store Abstraction"]
        STORE_STRUCT["Store struct"]
        CACHE_LAYER["Cache Layer<br/>userCache, userSettingCache<br/>workspaceSettingCache"]
    end
    
    SL --> STORE_STRUCT
    STORE_STRUCT --> CACHE_LAYER
    
    %% Driver Interface
    STORE_STRUCT --> DRIVER_IFACE["Driver interface"]
    
    %% Database Drivers
    subgraph DD["Database Drivers"]
        SQLITE_DB["sqlite.DB"]
        MYSQL_DB["mysql.DB"]
        POSTGRES_DB["postgres.DB"]
    end
    
    DRIVER_IFACE --> SQLITE_DB
    DRIVER_IFACE --> MYSQL_DB
    DRIVER_IFACE --> POSTGRES_DB
    
    %% Physical Storage
    subgraph PS["Physical Storage"]
        SQLITE_FILE["SQLite File"]
        MYSQL_SERVER["MySQL Server"]
        POSTGRES_SERVER["PostgreSQL Server"]
    end
    
    SQLITE_DB --> SQLITE_FILE
    MYSQL_DB --> MYSQL_SERVER
    POSTGRES_DB --> POSTGRES_SERVER
    
    %% Styling
    classDef service fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef store fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef driver fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef physical fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    
    class SL,MEMO_SVC,USER_SVC,WS_SVC service
    class SA,STORE_STRUCT,CACHE_LAYER store
    class DRIVER_IFACE,DD,SQLITE_DB,MYSQL_DB,POSTGRES_DB driver
    class PS,SQLITE_FILE,MYSQL_SERVER,POSTGRES_SERVER physical
```

`Store` 结构体充当中央数据访问层，为所有数据库操作提供统一接口。它包括内置缓存，并通过 `Driver` 接口抽象底层数据库实现。

### 存储接口和驱动程序模式

**来源：** [store/memo.go 114-147](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L114-L147) [store/db/sqlite/memo.go 16-265](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/memo.go#L16-L265) [store/db/mysql/memo.go 16-287](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/memo.go#L16-L287) [store/db/postgres/memo.go 16-279](https://github.com/usememos/memos/blob/50a41a39/store/db/postgres/memo.go#L16-L279)

```mermaid
flowchart TD
    %% Store Operations
    subgraph SO["Store Operations"]
        CREATE_MEMO["s.CreateMemo()"]
        LIST_MEMOS["s.ListMemos()"]
        GET_MEMO["s.GetMemo()"]
        UPDATE_MEMO["s.UpdateMemo()"]
        DELETE_MEMO["s.DeleteMemo()"]
    end
    
    %% Driver Interface
    subgraph DI["Driver Interface"]
        DRIVER_CREATE["driver.CreateMemo()"]
        DRIVER_LIST["driver.ListMemos()"]
        DRIVER_UPDATE["driver.UpdateMemo()"]
        DRIVER_DELETE["driver.DeleteMemo()"]
    end
    
    CREATE_MEMO --> DRIVER_CREATE
    LIST_MEMOS --> DRIVER_LIST
    UPDATE_MEMO --> DRIVER_UPDATE
    DELETE_MEMO --> DRIVER_DELETE
    
    %% Database-Specific Implementation
    subgraph DSI["Database-Specific Implementation"]
        SQLITE_CREATE["sqlite.CreateMemo()"]
        MYSQL_CREATE["mysql.CreateMemo()"]
        POSTGRES_CREATE["postgres.CreateMemo()"]
    end
    
    DRIVER_CREATE --> SQLITE_CREATE
    DRIVER_CREATE --> MYSQL_CREATE
    DRIVER_CREATE --> POSTGRES_CREATE
    
    %% Styling
    classDef store fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef driver fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef implementation fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    
    class SO,CREATE_MEMO,LIST_MEMOS,GET_MEMO,UPDATE_MEMO,DELETE_MEMO store
    class DI,DRIVER_CREATE,DRIVER_LIST,DRIVER_UPDATE,DRIVER_DELETE driver
    class DSI,SQLITE_CREATE,MYSQL_CREATE,POSTGRES_CREATE implementation
```

## 核心数据模型

应用程序定义了几个核心数据模型，代表系统中的主要实体。每个模型都遵循一致的模式，具有标准字段和特定于领域的属性。

### 主要数据模型

| 模型 | 目的 | 关键字段 | 存储位置 |
|------|------|----------|----------|
| `Memo` | 核心笔记/备忘录内容 | `ID`, `UID`, `Content`, `Visibility`, `Payload` | [store/memo.go 36-56](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L36-L56) |
| `User` | 用户账户和配置文件 | `ID`, `Username`, `Role`, `Email`, `PasswordHash` | [store/user.go 45-61](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L45-L61) |
| `WorkspaceSetting` | 全局配置 | `Name`, `Value`, `Description` | [store/workspace_setting.go 12-16](https://github.com/usememos/memos/blob/50a41a39/store/workspace_setting.go#L12-L16) |
| `UserSetting` | 每用户偏好设置 | `UserID`, `Key`, `Value` | [store/user_setting.go 13-17](https://github.com/usememos/memos/blob/50a41a39/store/user_setting.go#L13-L17) |
| `IdentityProvider` | 身份验证提供商 | `ID`, `Name`, `Type`, `Config` | [store/idp.go 12-18](https://github.com/usememos/memos/blob/50a41a39/store/idp.go#L12-L18) |
| `MemoRelation` | 备忘录关系 | `MemoID`, `RelatedMemoID`, `Type` | [store/driver.go 40-43](https://github.com/usememos/memos/blob/50a41a39/store/driver.go#L40-L43) |
| `Attachment` | 文件附件 | `ID`, `CreatorID`, `Filename`, `Size` | [store/driver.go 28-32](https://github.com/usememos/memos/blob/50a41a39/store/driver.go#L28-L32) |

### 备忘录数据模型

**来源：** [store/memo.go 36-56](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L36-L56) [store/memo.go 12-34](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L12-L34) [proto/gen/store/memo.pb.go 24-82](https://github.com/usememos/memos/blob/50a41a39/proto/gen/store/memo.pb.go#L24-L82) [proto/store/memo.proto 7-29](https://github.com/usememos/memos/blob/50a41a39/proto/store/memo.proto#L7-L29)

```mermaid
flowchart TD
    %% Memo Structure
    subgraph MS["Memo Structure"]
        MEMO_STRUCT["Memo struct"]
    end
    
    %% Standard Fields
    subgraph SF["Standard Fields"]
        STANDARD["ID, UID, RowStatus<br/>CreatorID, CreatedTs, UpdatedTs"]
    end
    
    MEMO_STRUCT --> STANDARD
    
    %% Domain Fields
    subgraph DF["Domain Fields"]
        DOMAIN["Content, Visibility<br/>Pinned, Payload"]
        COMPOSED["Composed Fields<br/>ParentID"]
    end
    
    MEMO_STRUCT --> DOMAIN
    MEMO_STRUCT --> COMPOSED
    
    %% Supporting Types
    subgraph ST["Supporting Types"]
        VISIBILITY["Visibility enum<br/>PUBLIC, PROTECTED, PRIVATE"]
        ROW_STATUS["RowStatus enum<br/>NORMAL, ARCHIVED"]
    end
    
    DOMAIN --> VISIBILITY
    DOMAIN --> ROW_STATUS
    
    %% MemoPayload Details
    subgraph MPD["MemoPayload Details"]
        MEMO_PAYLOAD["MemoPayload protobuf<br/>Property, Location, Tags"]
        PROPERTY["Property<br/>hasLink, hasTaskList<br/>hasCode, hasIncompleteTasks<br/>references"]
        LOCATION["Location<br/>placeholder, latitude, longitude"]
        TAGS["Tags<br/>string array"]
    end
    
    DOMAIN --> MEMO_PAYLOAD
    MEMO_PAYLOAD --> PROPERTY
    MEMO_PAYLOAD --> LOCATION
    MEMO_PAYLOAD --> TAGS
    
    %% Styling
    classDef memo fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef fields fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef types fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef payload fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    
    class MS,MEMO_STRUCT memo
    class SF,STANDARD,DF,DOMAIN,COMPOSED fields
    class ST,VISIBILITY,ROW_STATUS types
    class MPD,MEMO_PAYLOAD,PROPERTY,LOCATION,TAGS payload
```

`Memo` 结构体包括标准审计字段（`CreatedTs`、`UpdatedTs`、`RowStatus`）、特定于领域的内容字段，以及用于存储为 protobuf 的扩展元数据的灵活 `Payload` 字段。

### 用户和角色系统

**来源：** [store/user.go 7-43](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L7-L43) [store/user.go 45-61](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L45-L61)

```mermaid
flowchart TD
    %% User Model
    subgraph UM["User Model"]
        USER_STRUCT["User struct"]
        USER_FIELDS["ID, Username, Role<br/>Email, Nickname<br/>PasswordHash, AvatarURL"]
    end
    
    USER_STRUCT --> USER_FIELDS
    
    %% Role Hierarchy
    subgraph RH["Role Hierarchy"]
        HOST["HOST<br/>(System Owner)"]
        ADMIN["ADMIN<br/>(Administrator)"]
        USER_ROLE["USER<br/>(Regular User)"]
    end
    
    USER_FIELDS --> HOST
    HOST --> ADMIN
    ADMIN --> USER_ROLE
    
    %% Special Users
    subgraph SU["Special Users"]
        SYSTEM_BOT["SystemBot<br/>ID: 0<br/>Role: ADMIN"]
    end
    
    USER_STRUCT --> SYSTEM_BOT
    
    %% Styling
    classDef user fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef role fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef special fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    
    class UM,USER_STRUCT,USER_FIELDS user
    class RH,HOST,ADMIN,USER_ROLE role
    class SU,SYSTEM_BOT special
```

系统包括一个内置的 `SystemBot` 用户，ID 为 0，角色为 ADMIN，用于系统生成的内容和操作。

## 数据库访问层

数据库访问层通过一致的接口模式为所有实体提供 CRUD 操作。每个实体都遵循相同的模式，包含创建、列表/获取、更新和删除操作。

### CRUD 操作模式

**来源：** [store/memo.go 58-87](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L58-L87) [store/memo.go 89-96](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L89-L96) [store/memo.go 98-108](https://github.com/usememos/memos/blob/50a41a39/store/memo.go#L98-L108) [store/user.go 78-88](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L78-L88)

```mermaid
flowchart TD
    %% Operation Types
    subgraph OT["Operation Types"]
        CREATE_OPS["Create Operations<br/>CreateMemo(), CreateUser()"]
        FIND_OPS["Find Operations<br/>ListMemos(), GetMemo()"]
        UPDATE_OPS["Update Operations<br/>UpdateMemo(), UpdateUser()"]
        DELETE_OPS["Delete Operations<br/>DeleteMemo(), DeleteUser()"]
    end

    %% Find Patterns
    subgraph FP["Find Patterns"]
        FIND_STRUCTS["Find{Entity} structs<br/>FindMemo, FindUser"]
        FILTER_FIELDS["Filter Fields<br/>ID, CreatorID, Timestamps<br/>ContentSearch, VisibilityList"]
        PAGINATION["Pagination<br/>Limit, Offset"]
        ORDERING["Ordering<br/>OrderByUpdatedTs, OrderByPinned<br/>OrderByTimeAsc"]
    end

    FIND_OPS --> FIND_STRUCTS
    FIND_STRUCTS --> FILTER_FIELDS
    FIND_STRUCTS --> PAGINATION
    FIND_STRUCTS --> ORDERING

    %% FindMemo Specific
    subgraph FMS["FindMemo Specific"]
        PAYLOAD_FIND["PayloadFind<br/>TagSearch, HasLink<br/>HasTaskList, HasCode<br/>HasIncompleteTasks"]
        CONTENT_FILTERS["Content Filters<br/>ExcludeContent, ExcludeComments"]
        ADVANCED_FILTERS["Advanced Filters<br/>Filter (CEL expressions)"]
    end

    FIND_STRUCTS --> PAYLOAD_FIND
    FIND_STRUCTS --> CONTENT_FILTERS
    FIND_STRUCTS --> ADVANCED_FILTERS

    %% Update Patterns
    subgraph UP["Update Patterns"]
        UPDATE_STRUCTS["Update{Entity} structs<br/>UpdateMemo, UpdateUser"]
        POINTER_FIELDS["Pointer Fields<br/>*string, *int32, *bool"]
        PARTIAL_UPDATES["Partial Updates<br/>Only non-nil fields updated"]
    end

    UPDATE_OPS --> UPDATE_STRUCTS
    UPDATE_STRUCTS --> POINTER_FIELDS
    UPDATE_STRUCTS --> PARTIAL_UPDATES

    %% Styling
    classDef operations fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef find fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef specific fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef update fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000

    class OT,CREATE_OPS,FIND_OPS,UPDATE_OPS,DELETE_OPS operations
    class FP,FIND_STRUCTS,FILTER_FIELDS,PAGINATION,ORDERING find
    class FMS,PAYLOAD_FIND,CONTENT_FILTERS,ADVANCED_FILTERS specific
    class UP,UPDATE_STRUCTS,POINTER_FIELDS,PARTIAL_UPDATES update
```

### 查询构建和执行

数据库驱动程序实现查询构建，具有动态 WHERE 子句、参数化查询和特定于数据库的优化。

**来源：** [store/db/sqlite/memo.go 42-209](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/memo.go#L42-L209) [store/db/mysql/memo.go 50-218](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/memo.go#L50-L218) [store/db/postgres/memo.go 41-210](https://github.com/usememos/memos/blob/50a41a39/store/db/postgres/memo.go#L41-L210)

## 缓存系统

存储层包括多级缓存系统，以提高频繁访问数据的性能。

### 缓存架构

**来源：** [store/store.go 14-44](https://github.com/usememos/memos/blob/50a41a39/store/store.go#L14-L44) [store/user.go 100-149](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L100-L149) [store/user_setting.go 65-88](https://github.com/usememos/memos/blob/50a41a39/store/user_setting.go#L65-L88) [store/workspace_setting.go 82-99](https://github.com/usememos/memos/blob/50a41a39/store/workspace_setting.go#L82-L99)

```mermaid
flowchart TD
    %% Cache Types
    subgraph CT["Cache Types"]
        USER_CACHE["userCache<br/>User entities by ID<br/>SystemBot handling"]
        USER_SETTING_CACHE["userSettingCache<br/>User settings by key"]
        WS_SETTING_CACHE["workspaceSettingCache<br/>Workspace settings"]
    end

    %% Cache Configuration
    subgraph CC["Cache Configuration"]
        CACHE_CONFIG["cache.Config"]
        DEFAULT_TTL["DefaultTTL: 10min"]
        CLEANUP_INTERVAL["CleanupInterval: 5min"]
        MAX_ITEMS["MaxItems: 1000"]
        ON_EVICTION["OnEviction: nil"]
    end

    CT --> CACHE_CONFIG
    CACHE_CONFIG --> DEFAULT_TTL
    CACHE_CONFIG --> CLEANUP_INTERVAL
    CACHE_CONFIG --> MAX_ITEMS
    CACHE_CONFIG --> ON_EVICTION

    %% Cache Operations
    subgraph CO["Cache Operations"]
        CACHE_GET["cache.Get(ctx, key)"]
        CACHE_SET["cache.Set(ctx, key, value)"]
        CACHE_DELETE["cache.Delete(ctx, key)"]
        CACHE_CLOSE["cache.Close()"]
    end

    CT --> CACHE_GET
    CT --> CACHE_SET
    CT --> CACHE_DELETE
    CT --> CACHE_CLOSE

    %% Styling
    classDef cache fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef config fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef operations fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000

    class CT,USER_CACHE,USER_SETTING_CACHE,WS_SETTING_CACHE cache
    class CC,CACHE_CONFIG,DEFAULT_TTL,CLEANUP_INTERVAL,MAX_ITEMS,ON_EVICTION config
    class CO,CACHE_GET,CACHE_SET,CACHE_DELETE,CACHE_CLOSE operations
```

缓存系统自动管理 TTL、清理和驱逐策略。缓存键使用一致的模式，如 `getUserSettingCacheKey(userID, key)` 进行可预测的缓存管理。

### 缓存使用模式

缓存实现遵循读穿透和写穿透模式：

- **读穿透**: 缓存未命中触发数据库查询，对 `SystemBot`（ID: 0）进行特殊处理
- **写穿透**: 更新使缓存条目无效或刷新
- **缓存失效**: 实体删除时显式缓存删除
- **特殊情况**: `SystemBot` 立即返回，无需数据库访问

**来源：** [store/user.go 127-149](https://github.com/usememos/memos/blob/50a41a39/store/user.go#L127-L149) [store/user_setting.go 66-73](https://github.com/usememos/memos/blob/50a41a39/store/user_setting.go#L66-L73) [store/workspace_setting.go 82-99](https://github.com/usememos/memos/blob/50a41a39/store/workspace_setting.go#L82-L99)

## 迁移和模式管理

系统包括用于数据库模式版本控制和升级的综合迁移系统。

### 迁移系统架构

**来源：** [store/migrator.go 21-149](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L21-L149) [store/migrator.go 152-190](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L152-L190) [store/migrator.go 193-226](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L193-L226)

```mermaid
flowchart TD
    %% Migration Files
    subgraph MF["Migration Files"]
        MIGRATION_FS["migrationFS<br/>embed.FS //go:embed migration"]
        SEED_FS["seedFS<br/>embed.FS //go:embed seed"]
        VERSIONED_DIRS["Versioned Directories<br/>migration/{driver}/"]
        MIGRATION_SCRIPTS["Migration Scripts<br/>1__create_table.sql"]
        LATEST_SQL["LATEST.sql<br/>Complete schema"]
    end

    MIGRATION_FS --> VERSIONED_DIRS
    VERSIONED_DIRS --> MIGRATION_SCRIPTS
    VERSIONED_DIRS --> LATEST_SQL

    %% Migration Process
    subgraph MP["Migration Process"]
        PRE_MIGRATE["preMigrate()<br/>Check initialization"]
        DRIVER_INIT["driver.IsInitialized()"]
        APPLY_LATEST["Apply LATEST.sql<br/>for fresh installs"]
        EXECUTE_PENDING["Execute pending migrations<br/>production mode"]
        SEED_DEMO["seed() demo data<br/>demo mode only"]
    end

    PRE_MIGRATE --> DRIVER_INIT
    DRIVER_INIT --> APPLY_LATEST
    DRIVER_INIT --> EXECUTE_PENDING
    EXECUTE_PENDING --> SEED_DEMO

    %% Version Management
    subgraph VM["Version Management"]
        MIGRATION_HISTORY["migration_history table<br/>normalizeMigrationHistoryList()"]
        WS_SCHEMA_VERSION["WorkspaceBasicSetting.SchemaVersion"]
        VERSION_COMPARE["version.IsVersionGreaterThan()"]
        UPDATE_VERSION["updateCurrentSchemaVersion()"]
    end

    EXECUTE_PENDING --> MIGRATION_HISTORY
    MIGRATION_HISTORY --> WS_SCHEMA_VERSION
    WS_SCHEMA_VERSION --> VERSION_COMPARE
    VERSION_COMPARE --> UPDATE_VERSION

    %% Styling
    classDef files fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef process fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef version fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000

    class MF,MIGRATION_FS,SEED_FS,VERSIONED_DIRS,MIGRATION_SCRIPTS,LATEST_SQL files
    class MP,PRE_MIGRATE,DRIVER_INIT,APPLY_LATEST,EXECUTE_PENDING,SEED_DEMO process
    class VM,MIGRATION_HISTORY,WS_SCHEMA_VERSION,VERSION_COMPARE,UPDATE_VERSION version
```

### 迁移执行流程

迁移系统支持多种模式：

- **全新安装**: 应用包含完整模式的 `LATEST.sql`
- **生产升级**: 执行带事务支持的增量迁移脚本
- **演示模式**: 用于演示目的的附加种子数据
- **模式版本控制**: 在 `WorkspaceBasicSetting` 和 `migration_history` 中跟踪版本

**来源：** [store/migrator.go 36-103](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L36-L103) [store/migrator.go 106-149](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L106-L149) [store/migrator.go 160-190](https://github.com/usememos/memos/blob/50a41a39/store/migrator.go#L160-L190)

## 使用 CEL 进行查询过滤

系统使用通用表达式语言（CEL）实现高级查询过滤，用于复杂的备忘录搜索和过滤。

### CEL 过滤器架构

**来源：** [plugin/filter/filter.go 13-47](https://github.com/usememos/memos/blob/50a41a39/plugin/filter/filter.go#L13-47) [store/db/sqlite/memo_filter.go 14-236](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/memo_filter.go#L14-L236) [store/db/mysql/memo_filter.go 14-238](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/memo_filter.go#L14-L238) [store/db/postgres/memo_filter.go 14-230](https://github.com/usememos/memos/blob/50a41a39/store/db/postgres/memo_filter.go#L14-L230)

### 过滤器表达式示例

CEL 过滤器系统支持具有特定于数据库的优化的复杂查询：

- **标签过滤**: `tag in ["work", "personal"]`
- **内容搜索**: `content.contains("meeting")`
- **日期范围**: `created_ts > now() - 60 * 60 * 24`
- **复杂组合**: `has_task_list && pinned && visibility in ["PUBLIC"]`

每个数据库驱动程序为 JSON 操作实现优化的 SQL 生成，SQLite 使用 `JSON_EXTRACT()`，MySQL 使用 `JSON_CONTAINS()`，PostgreSQL 使用原生 JSONB 操作符。

**来源：** [store/db/sqlite/memo_filter_test.go 12-125](https://github.com/usememos/memos/blob/50a41a39/store/db/sqlite/memo_filter_test.go#L12-L125) [store/db/mysql/memo_filter_test.go 12-110](https://github.com/usememos/memos/blob/50a41a39/store/db/mysql/memo_filter_test.go#L12-L110) [store/db/postgres/memo_filter_test.go 12-110](https://github.com/usememos/memos/blob/50a41a39/store/db/postgres/memo_filter_test.go#L12-L110) [plugin/filter/expr.go 42-127](https://github.com/usememos/memos/blob/50a41a39/plugin/filter/expr.go#L42-L127)

## 总结

Memos 数据模型和存储架构提供了一个强大且灵活的数据持久化层。分层设计确保了数据库无关性，而缓存系统提高了性能。CEL 过滤器系统支持复杂查询，迁移系统确保平滑的模式演进。这种架构支持应用程序的可扩展性和可维护性需求。
