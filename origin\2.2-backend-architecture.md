# 后端架构

> 📅 **最后索引**: 2025年6月30日 ([50a41a](https://github.com/usememos/memos/commits/50a41a39))

<details>
<summary><strong>🔍 点击展开查看核心源文件列表</strong></summary>

**核心后端文件:**

- [server/server.go](https://github.com/usememos/memos/blob/50a41a39/server/server.go) - 主服务器结构和初始化
- [server/router/api/v1/](https://github.com/usememos/memos/tree/50a41a39/server/router/api/v1) - API 服务实现
- [store/](https://github.com/usememos/memos/tree/50a41a39/store) - 存储层抽象

**Protocol Buffer 定义:**

- [proto/api/v1/memo_service.proto](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/memo_service.proto) - 备忘录服务定义
- [proto/api/v1/auth_service.proto](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/auth_service.proto) - 认证服务定义
- [proto/gen/](https://github.com/usememos/memos/tree/50a41a39/proto/gen) - 生成的 Go 代码

**服务实现:**

- [server/router/api/v1/memo_service.go](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go) - 备忘录服务
- [server/router/api/v1/auth_service.go](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/auth_service.go) - 认证服务
- [server/router/api/v1/acl_config.go](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/acl_config.go) - 访问控制配置

</details>

---

## 目的和范围

本文档描述了 Memos 应用程序基于 Go 的后端架构，涵盖服务器基础设施、服务层组织、API 设计和数据流模式。它专注于后端服务的技术实现细节、gRPC/HTTP 网关集成和存储抽象层。

有关前端架构详细信息，请参阅 [前端架构](./2.1-frontend-architecture.md)。有关数据模型和存储详细信息，请参阅 [数据模型和存储](./2.3-data-models-and-storage.md)。

## 整体系统架构

后端遵循分层架构，在 HTTP/gRPC 协议处理、业务逻辑服务和数据持久化之间有明确的分离。系统围绕单个 Go 服务器构建，该服务器在同一端口上复用 HTTP 和 gRPC 流量。

**来源：** [server/server.go 31-40](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L31-L40) [server/router/api/v1/memo_service.go 1-27](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go#L1-L27)

```mermaid
flowchart TD
    %% External
    subgraph EXT["External"]
        CLIENT["Client"]
    end
    
    %% Server Layer
    subgraph SL["Server Layer"]
        SERVER["server.Server"]
        CMUX["cmux.New()"]
        ECHO["echo.Echo"]
        GRPC["grpc.Server"]
    end
    
    CLIENT --> SERVER
    SERVER --> CMUX
    CMUX --> ECHO
    CMUX --> GRPC
    
    %% API Layer
    subgraph AL["API Layer"]
        APIV1["v1.APIV1Service"]
        GATEWAY["grpc-gateway mux"]
        INTERCEPTORS["Auth & Logger Interceptors"]
    end
    
    ECHO --> GATEWAY
    GRPC --> INTERCEPTORS
    INTERCEPTORS --> APIV1
    GATEWAY --> APIV1
    
    %% Service Layer
    subgraph SER["Service Layer"]
        MEMO_SVC["MemoService"]
        AUTH_SVC["AuthService"]
        USER_SVC["UserService"]
        MD_SVC["MarkdownService"]
        WS_SVC["WorkspaceService"]
        ATT_SVC["AttachmentService"]
    end
    
    APIV1 --> MEMO_SVC
    APIV1 --> AUTH_SVC
    APIV1 --> USER_SVC
    APIV1 --> MD_SVC
    APIV1 --> WS_SVC
    APIV1 --> ATT_SVC
    
    %% Store Layer
    subgraph ST["Store Layer"]
        STORE["store.Store"]
        DRIVERS["Database Drivers"]
    end
    
    MEMO_SVC --> STORE
    AUTH_SVC --> STORE
    USER_SVC --> STORE
    MD_SVC --> STORE
    WS_SVC --> STORE
    ATT_SVC --> STORE
    
    STORE --> DRIVERS
    
    %% External Storage
    subgraph ES["External Storage"]
        DATABASE["Database"]
        FILE_STORAGE["File Storage"]
    end
    
    DRIVERS --> DATABASE
    STORE --> FILE_STORAGE
    
    %% Styling
    classDef external fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef server fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef api fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef service fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef store fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    classDef storage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000000
    
    class EXT,CLIENT external
    class SL,SERVER,CMUX,ECHO,GRPC server
    class AL,APIV1,GATEWAY,INTERCEPTORS api
    class SER,MEMO_SVC,AUTH_SVC,USER_SVC,MD_SVC,WS_SVC,ATT_SVC service
    class ST,STORE,DRIVERS store
    class ES,DATABASE,FILE_STORAGE storage
```

## 服务器基础设施和初始化

服务器初始化过程由 `Server` 结构体协调，该结构体协调多个组件，包括 Echo HTTP 服务器、gRPC 服务器和连接多路复用器。

**来源：** [server/server.go 42-101](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L42-L101) [server/server.go 103-139](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L103-L139)

```mermaid
flowchart TD
    %% Server Initialization
    subgraph SI["Server Initialization"]
        NEW_SERVER["NewServer()"]
    end
    
    %% Configuration
    subgraph CONF["Configuration"]
        WS_SETTINGS["Workspace Settings"]
        SECRET_KEY["Secret Key Generation"]
    end
    
    NEW_SERVER --> WS_SETTINGS
    NEW_SERVER --> SECRET_KEY
    
    %% Runtime Components
    subgraph RC["Runtime Components"]
        ECHO_SETUP["Echo Server Setup"]
        GRPC_SETUP["gRPC Server Setup"]
        PROFILER_SETUP["Profiler Setup"]
        SERVICE_REG["Service Registration"]
    end
    
    NEW_SERVER --> ECHO_SETUP
    NEW_SERVER --> GRPC_SETUP
    NEW_SERVER --> PROFILER_SETUP
    NEW_SERVER --> SERVICE_REG
    
    %% Connection Multiplexing
    subgraph CM["Connection Multiplexing"]
        CMUX_NEW["cmux.New(listener)"]
        HTTP1_LISTENER["HTTP1Fast Listener"]
        HTTP2_LISTENER["HTTP2 gRPC Listener"]
    end
    
    SERVICE_REG --> CMUX_NEW
    CMUX_NEW --> HTTP1_LISTENER
    CMUX_NEW --> HTTP2_LISTENER
    
    %% Background Services
    subgraph BG["Background Services"]
        BACKGROUND_RUNNERS["Background Runners"]
        PROFILE_PROF["profile.Profile"]
    end
    
    PROFILER_SETUP --> BACKGROUND_RUNNERS
    BACKGROUND_RUNNERS --> PROFILE_PROF
    
    %% Styling
    classDef init fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef config fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef runtime fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef multiplexing fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000
    classDef background fill:#fce4ec,stroke:#880e4f,stroke-width:2px,color:#000000
    
    class SI,NEW_SERVER init
    class CONF,WS_SETTINGS,SECRET_KEY config
    class RC,ECHO_SETUP,GRPC_SETUP,PROFILER_SETUP,SERVICE_REG runtime
    class CM,CMUX_NEW,HTTP1_LISTENER,HTTP2_LISTENER multiplexing
    class BG,BACKGROUND_RUNNERS,PROFILE_PROF background
```

服务器使用连接多路复用在单个端口上处理 HTTP 和 gRPC 协议。`cmux` 库根据协议头路由传入连接。

### 关键服务器组件

| 组件 | 文件位置 | 目的 |
|------|----------|------|
| `Server` | [server/server.go 31-40](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L31-L40) | 协调所有组件的主服务器结构 |
| `Echo` | [server/server.go 48-53](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L48-L53) | 用于静态文件和健康端点的 HTTP 服务器 |
| `grpc.Server` | [server/server.go 84-92](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L84-L92) | 带拦截器的 gRPC 服务器 |
| `cmux.New` | [server/server.go 117-135](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L117-L135) | HTTP/gRPC 连接多路复用器 |
| `Profiler` | [server/server.go 55-58](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L55-L58) | 内存和性能监控 |

## 服务层架构

API 围绕实现 gRPC 服务接口和业务逻辑的特定领域服务组织。所有服务都通过 `APIV1Service` 协调，它充当主要的服务容器。

**来源：** [server/router/api/v1/memo_service.go 29-95](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go#L29-L95) [server/router/api/v1/auth_service.go 32-60](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/auth_service.go#L32-60)

```mermaid
flowchart TD
    %% APIV1Service Container
    subgraph ASC["APIV1Service Container"]
        APIV1_SVC["v1.APIV1Service"]
        SECRET_KEY["Secret Key"]
        PROFILE["profile.Profile"]
        STORE_REF["store.Store"]
        GRPC_SERVER["grpc.Server"]
    end

    %% Domain Services
    subgraph DS["Domain Services"]
        MEMO_METHODS["MemoService Methods"]
        AUTH_METHODS["AuthService Methods"]
        USER_METHODS["UserService Methods"]
        MD_METHODS["MarkdownService Methods"]
        WS_METHODS["WorkspaceService Methods"]
        ATT_METHODS["AttachmentService Methods"]
    end

    APIV1_SVC --> MEMO_METHODS
    APIV1_SVC --> AUTH_METHODS
    APIV1_SVC --> USER_METHODS
    APIV1_SVC --> MD_METHODS
    APIV1_SVC --> WS_METHODS
    APIV1_SVC --> ATT_METHODS

    %% Core Methods
    subgraph CM["Core Methods"]
        CREATE_MEMO["CreateMemo()"]
        LIST_MEMOS["ListMemos()"]
        GET_MEMO["GetMemo()"]
        UPDATE_MEMO["UpdateMemo()"]
        DELETE_MEMO["DeleteMemo()"]
        CREATE_SESSION["CreateSession()"]
        GET_SESSION["GetCurrentSession()"]
        DELETE_SESSION["DeleteSession()"]
        PARSE_MD["ParseMarkdown()"]
        GET_LINK["GetLinkMetadata()"]
    end

    MEMO_METHODS --> CREATE_MEMO
    MEMO_METHODS --> LIST_MEMOS
    MEMO_METHODS --> GET_MEMO
    MEMO_METHODS --> UPDATE_MEMO
    MEMO_METHODS --> DELETE_MEMO

    AUTH_METHODS --> CREATE_SESSION
    AUTH_METHODS --> GET_SESSION
    AUTH_METHODS --> DELETE_SESSION

    MD_METHODS --> PARSE_MD
    MD_METHODS --> GET_LINK

    %% Styling
    classDef container fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef domain fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef methods fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000

    class ASC,APIV1_SVC,SECRET_KEY,PROFILE,STORE_REF,GRPC_SERVER container
    class DS,MEMO_METHODS,AUTH_METHODS,USER_METHODS,MD_METHODS,WS_METHODS,ATT_METHODS domain
    class CM,CREATE_MEMO,LIST_MEMOS,GET_MEMO,UPDATE_MEMO,DELETE_MEMO,CREATE_SESSION,GET_SESSION,DELETE_SESSION,PARSE_MD,GET_LINK methods
```

### 服务方法实现模式

每个服务方法都遵循一致的模式：

1. **身份验证**: 从上下文中提取和验证当前用户
2. **授权**: 基于用户角色和资源所有权检查权限
3. **验证**: 验证请求参数和业务规则
4. **业务逻辑**: 执行核心功能
5. **数据持久化**: 与存储层交互
6. **响应**: 将存储对象转换为 protocol buffer 消息

来自 `CreateMemo` 的示例：

```go
// 1. Authentication
user, err := s.GetCurrentUser(ctx)

// 2. Authorization
if workspaceMemoRelatedSetting.DisallowPublicVisibility && create.Visibility == store.Public {
    return nil, status.Errorf(codes.PermissionDenied, "disable public memos system setting is enabled")
}

// 3. Validation
if len(create.Content) > contentLengthLimit {
    return nil, status.Errorf(codes.InvalidArgument, "content too long (max %d characters)", contentLengthLimit)
}

// 4. Business Logic
if err := memopayload.RebuildMemoPayload(create); err != nil {
    return nil, status.Errorf(codes.Internal, "failed to rebuild memo payload: %v", err)
}

// 5. Data Persistence
memo, err := s.Store.CreateMemo(ctx, create)

// 6. Response
memoMessage, err := s.convertMemoFromStore(ctx, memo)
```

**来源：** [server/router/api/v1/memo_service.go 29-95](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go#L29-L95)

## gRPC 和 HTTP 网关集成

系统使用 `grpc-gateway` 从 gRPC 服务定义自动生成 RESTful HTTP API。这从单个服务实现提供原生 gRPC 和 HTTP JSON API。

**来源：** [proto/gen/api/v1/memo_service.pb.gw.go 1-25](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/memo_service.pb.gw.go#L1-L25) [proto/gen/api/v1/auth_service.pb.gw.go 1-25](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/auth_service.pb.gw.go#L1-L25)

```mermaid
flowchart TD
    %% Client Requests
    subgraph CR["Client Requests"]
        HTTP_CLIENT["HTTP/JSON Client"]
        GRPC_CLIENT["gRPC Client"]
    end

    %% Protocol Layer
    subgraph PL["Protocol Layer"]
        GATEWAY_MUX["grpc-gateway Mux"]
        GRPC_SRV["gRPC Server"]
    end

    HTTP_CLIENT --> GATEWAY_MUX
    GRPC_CLIENT --> GRPC_SRV
    GATEWAY_MUX --> GRPC_SRV

    %% Service Implementation
    subgraph SI["Service Implementation"]
        APIV1_IMPL["APIV1Service"]
        SERVICE_METHODS["Service Methods"]
    end

    GRPC_SRV --> APIV1_IMPL
    APIV1_IMPL --> SERVICE_METHODS

    %% Generated Code
    subgraph GC["Generated Code"]
        PROTO_FILES["*.proto files"]
        PB_GO["*.pb.go"]
        PB_GW["*.pb.gw.go"]
        GRPC_PB["*_grpc.pb.go"]
    end

    PROTO_FILES --> PB_GO
    PROTO_FILES --> PB_GW
    PROTO_FILES --> GRPC_PB

    %% Styling
    classDef client fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef protocol fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef generated fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000

    class CR,HTTP_CLIENT,GRPC_CLIENT client
    class PL,GATEWAY_MUX,GRPC_SRV protocol
    class SI,APIV1_IMPL,SERVICE_METHODS service
    class GC,PROTO_FILES,PB_GO,PB_GW,GRPC_PB generated
```

### Protocol Buffer 模式组织

API 模式在 `.proto` 文件中定义并生成多个 Go 文件：

| 文件类型 | 目的 | 示例 |
|----------|------|------|
| `*.proto` | 服务和消息定义 | [proto/api/v1/memo_service.proto](https://github.com/usememos/memos/blob/50a41a39/proto/api/v1/memo_service.proto) |
| `*.pb.go` | Go 结构体和序列化 | [proto/gen/api/v1/memo_service.pb.go](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/memo_service.pb.go) |
| `*.pb.gw.go` | HTTP 网关路由 | [proto/gen/api/v1/memo_service.pb.gw.go](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/memo_service.pb.gw.go) |
| `*_grpc.pb.go` | gRPC 服务接口 | [proto/gen/api/v1/memo_service_grpc.pb.go](https://github.com/usememos/memos/blob/50a41a39/proto/gen/api/v1/memo_service_grpc.pb.go) |
| `apidocs.swagger.yaml` | OpenAPI 文档 | [proto/gen/apidocs.swagger.yaml](https://github.com/usememos/memos/blob/50a41a39/proto/gen/apidocs.swagger.yaml) |

## 身份验证和授权系统

身份验证系统支持基于会话和基于 JWT 的身份验证，通过 OAuth2/OIDC 支持外部身份提供商。

**来源：** [server/router/api/v1/auth_service.go 32-236](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/auth_service.go#L32-236) [server/router/api/v1/acl_config.go 3-34](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/acl_config.go#L3-L34)

```mermaid
flowchart TD
    %% Authentication Flow
    subgraph AF["Authentication Flow"]
        GRPC_AUTH["GRPCAuthInterceptor"]
        AUTH_ALLOWLIST["Authentication Allowlist"]
        SESSION_VALIDATION["Session Validation"]
        USER_CONTEXT["User Context Injection"]
    end

    %% Authentication Methods
    subgraph AM["Authentication Methods"]
        PASSWORD_AUTH["Password Authentication"]
        SSO_AUTH["SSO Authentication"]
        SESSION_COOKIE["Session Cookie"]
    end

    PASSWORD_AUTH --> SESSION_COOKIE
    SSO_AUTH --> SESSION_COOKIE

    %% Session Management
    subgraph SM["Session Management"]
        CREATE_SESSION_FUNC["CreateSession()"]
        SESSION_TRACKING["Session Tracking"]
        USER_SETTINGS["User Settings Store"]
        CLIENT_INFO["Client Info Extraction"]
    end

    SESSION_COOKIE --> CREATE_SESSION_FUNC
    CREATE_SESSION_FUNC --> SESSION_TRACKING
    SESSION_TRACKING --> USER_SETTINGS
    SESSION_TRACKING --> CLIENT_INFO

    %% Authorization
    subgraph AUTH["Authorization"]
        ACL_CONFIG["ACL Configuration"]
        ADMIN_ONLY["Admin-Only Methods"]
        RESOURCE_OWNERSHIP["Resource Ownership"]
    end

    USER_CONTEXT --> ACL_CONFIG
    ACL_CONFIG --> ADMIN_ONLY
    ACL_CONFIG --> RESOURCE_OWNERSHIP

    %% Styling
    classDef authFlow fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef authMethods fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef sessionMgmt fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef authorization fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000

    class AF,GRPC_AUTH,AUTH_ALLOWLIST,SESSION_VALIDATION,USER_CONTEXT authFlow
    class AM,PASSWORD_AUTH,SSO_AUTH,SESSION_COOKIE authMethods
    class SM,CREATE_SESSION_FUNC,SESSION_TRACKING,USER_SETTINGS,CLIENT_INFO sessionMgmt
    class AUTH,ACL_CONFIG,ADMIN_ONLY,RESOURCE_OWNERSHIP authorization

### 身份验证拦截器链

gRPC 服务器使用拦截器链进行请求处理：

- **Logger Interceptor**: 请求/响应日志记录
- **Recovery Interceptor**: 恐慌恢复和错误处理
- **Auth Interceptor**: 身份验证和授权

身份验证拦截器实现基于方法的允许列表：

```go
var authenticationAllowlistMethods = map[string]bool{
    "/memos.api.v1.WorkspaceService/GetWorkspaceProfile": true,
    "/memos.api.v1.AuthService/CreateSession": true,
    "/memos.api.v1.MemoService/GetMemo": true,
    "/memos.api.v1.MemoService/ListMemos": true,
    // ... other public methods
}
```

**来源：** [server/router/api/v1/acl_config.go 3-19](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/acl_config.go#L3-L19) [server/server.go 84-92](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L84-L92)

### 会话管理实现

会话管理包括全面的客户端跟踪和设备指纹识别：

```go
type SessionsUserSetting_Session struct {
    SessionId string
    CreateTime *timestamppb.Timestamp
    LastAccessedTime *timestamppb.Timestamp
    ClientInfo *SessionsUserSetting_ClientInfo
}

type SessionsUserSetting_ClientInfo struct {
    UserAgent string
    IpAddress string
    DeviceType string  // "mobile", "tablet", "desktop"
    Os string          // "iOS 17.1", "Windows 10/11"
    Browser string     // "Chrome 120.0.0.0"
}
```

**来源：** [server/router/api/v1/auth_service.go 302-351](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/auth_service.go#L302-L351)

## 请求处理流程

以下图表显示了请求如何从客户端流向数据存储：

**来源：** [server/server.go 117-135](https://github.com/usememos/memos/blob/50a41a39/server/server.go#L117-L135) [server/router/api/v1/memo_service.go 29-95](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go#L29-L95)

```mermaid
sequenceDiagram
    participant Client
    participant CMUX as cmux.New()
    participant Echo as echo.Echo
    participant Gateway as grpc-gateway
    participant Auth as Auth Interceptor
    participant Service as APIV1Service
    participant Store as store.Store
    participant DB as Database

    Note over Client,DB: HTTP/gRPC Request Processing

    Client->>CMUX: HTTP/gRPC Request

    alt HTTP Request
        CMUX->>Echo: Route to HTTP
        Echo->>Gateway: JSON to gRPC
        Gateway->>Auth: gRPC Call
    else gRPC Request
        CMUX->>Auth: Direct gRPC
    end

    Auth->>Auth: Validate Auth
    Auth->>Service: Authenticated Call
    Service->>Service: Business Logic
    Service->>Store: Data Operation
    Store->>DB: SQL Query
    DB-->>Store: Query Result
    Store-->>Service: Store Object
    Service->>Service: Convert to Proto
    Service-->>Client: Response
```

### 错误处理和状态码

系统在 gRPC 和 HTTP 接口中一致使用 gRPC 状态码：

| gRPC 代码 | HTTP 状态 | 使用示例 |
|-----------|-----------|----------|
| `codes.InvalidArgument` | 400 | 无效的请求参数 |
| `codes.Unauthenticated` | 401 | 缺少或无效的身份验证 |
| `codes.PermissionDenied` | 403 | 权限不足 |
| `codes.NotFound` | 404 | 资源未找到 |
| `codes.Internal` | 500 | 服务器端错误 |

**来源：** [server/router/api/v1/memo_service.go 32-33](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/memo_service.go#L32-L33) [server/router/api/v1/auth_service.go 35-42](https://github.com/usememos/memos/blob/50a41a39/server/router/api/v1/auth_service.go#L35-L42)

## 存储抽象层

存储层为数据持久化提供数据库无关的接口，通过驱动程序实现支持多个数据库后端。

**来源：** [store/](https://github.com/usememos/memos/blob/50a41a39/store/) [go.mod 11-21](https://github.com/usememos/memos/blob/50a41a39/go.mod#L11-L21) [go.mod 32-33](https://github.com/usememos/memos/blob/50a41a39/go.mod#L32-L33)

```mermaid
flowchart TD
    %% Store Interface
    subgraph SI["Store Interface"]
        STORE_IFACE["store.Store"]
    end

    %% Entity Operations
    subgraph EO["Entity Operations"]
        FIND_METHODS["Find* Methods"]
        CREATE_METHODS["Create* Methods"]
        UPDATE_METHODS["Update* Methods"]
        DELETE_METHODS["Delete* Methods"]
    end

    STORE_IFACE --> FIND_METHODS
    STORE_IFACE --> CREATE_METHODS
    STORE_IFACE --> UPDATE_METHODS
    STORE_IFACE --> DELETE_METHODS

    %% Specific Operations
    subgraph SO["Specific Operations"]
        MEMO_OPS["Memo Operations"]
        USER_OPS["User Operations"]
        ATT_OPS["Attachment Operations"]
        WS_OPS["Workspace Operations"]
        SESSION_OPS["Session Operations"]
    end

    FIND_METHODS --> MEMO_OPS
    CREATE_METHODS --> USER_OPS
    UPDATE_METHODS --> ATT_OPS
    DELETE_METHODS --> WS_OPS
    FIND_METHODS --> SESSION_OPS

    %% Database Drivers
    subgraph DD["Database Drivers"]
        SQLITE_DRIVER["SQLite Driver"]
        MYSQL_DRIVER["MySQL Driver"]
        POSTGRES_DRIVER["PostgreSQL Driver"]
    end

    MEMO_OPS --> SQLITE_DRIVER
    USER_OPS --> MYSQL_DRIVER
    ATT_OPS --> POSTGRES_DRIVER
    WS_OPS --> SQLITE_DRIVER
    SESSION_OPS --> MYSQL_DRIVER

    %% Styling
    classDef store fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px,color:#000000
    classDef operations fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000000
    classDef specific fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000000
    classDef drivers fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000000

    class SI,STORE_IFACE store
    class EO,FIND_METHODS,CREATE_METHODS,UPDATE_METHODS,DELETE_METHODS operations
    class SO,MEMO_OPS,USER_OPS,ATT_OPS,WS_OPS,SESSION_OPS specific
    class DD,SQLITE_DRIVER,MYSQL_DRIVER,POSTGRES_DRIVER drivers
```

存储抽象使后端能够与不同的数据库系统一起工作，而无需更改服务层代码。每个驱动程序实现相同的接口，但使用特定于数据库的 SQL 方言和功能。

## 总结

Memos 后端架构提供了一个强大且可扩展的系统，具有清晰的关注点分离。分层方法确保了可维护性，而 gRPC/HTTP 双协议支持提供了客户端集成的灵活性。存储抽象层支持多个数据库后端，身份验证系统提供全面的安全性和会话管理。
