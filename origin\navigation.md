# Memos 项目学习导航

> 基于 [DeepWiki](https://deepwiki.com/usememos/memos) 的完整学习路径
>
> 最后索引时间：2025年6月30日 ([50a41a](https://github.com/usememos/memos/commits/50a41a39))

## 🔗 快速导航

- 📖 [项目概览](./1-overview.md) - 了解整体架构和技术栈
- 📋 [文档说明](./README.md) - 查看文档结构和使用指南
- 🏗️ [架构详情](./2-architecture.md) - 深入理解系统设计 ⏳
- 🎨 [前端详情](./3-frontend.md) - 用户界面实现 ⏳
- ⚙️ [后端详情](./4-backend.md) - 服务端逻辑 ⏳
- 🚀 [部署指南](./5-deployment.md) - 生产环境配置 ⏳

## 📚 完整学习路径

### 1. 概览 (Overview)
- [1-overview.md](./1-overview.md) - 项目概览 ✅ **已完成**

### 2. 架构 (Architecture)
- [2-architecture.md](./2-architecture.md) - 架构总览 ✅ **已完成**
- [2.1-frontend-architecture.md](./2.1-frontend-architecture.md) - 前端架构 ⏳ 待学习
- [2.2-backend-architecture.md](./2.2-backend-architecture.md) - 后端架构 ⏳ 待学习
- [2.3-data-models-and-storage.md](./2.3-data-models-and-storage.md) - 数据模型和存储 ⏳ 待学习

### 3. 前端 (Frontend)
- [3-frontend.md](./3-frontend.md) - 前端总览 ⏳ 待学习
- [3.1-application-setup-and-routing.md](./3.1-application-setup-and-routing.md) - 应用设置和路由 ⏳ 待学习
- [3.2-memo-management-system.md](./3.2-memo-management-system.md) - 备忘录管理系统 ⏳ 待学习
- [3.3-pages-and-navigation.md](./3.3-pages-and-navigation.md) - 页面和导航 ⏳ 待学习
- [3.4-settings-and-configuration-ui.md](./3.4-settings-and-configuration-ui.md) - 设置和配置界面 ⏳ 待学习
- [3.5-authentication-and-user-management.md](./3.5-authentication-and-user-management.md) - 认证和用户管理 ⏳ 待学习
- [3.6-content-enhancement-features.md](./3.6-content-enhancement-features.md) - 内容增强功能 ⏳ 待学习
- [3.7-state-management.md](./3.7-state-management.md) - 状态管理 ⏳ 待学习
- [3.8-internationalization.md](./3.8-internationalization.md) - 国际化 ⏳ 待学习
- [3.9-ui-framework-and-utilities.md](./3.9-ui-framework-and-utilities.md) - UI框架和工具 ⏳ 待学习

### 4. 后端 (Backend)
- [4-backend.md](./4-backend.md) - 后端总览 ⏳ 待学习
- [4.1-server-infrastructure.md](./4.1-server-infrastructure.md) - 服务器基础设施 ⏳ 待学习
- [4.2-memo-service.md](./4.2-memo-service.md) - 备忘录服务 ⏳ 待学习
- [4.3-markdown-processing-service.md](./4.3-markdown-processing-service.md) - Markdown处理服务 ⏳ 待学习
- [4.4-user-and-authentication-services.md](./4.4-user-and-authentication-services.md) - 用户和认证服务 ⏳ 待学习
- [4.5-workspace-and-settings-services.md](./4.5-workspace-and-settings-services.md) - 工作空间和设置服务 ⏳ 待学习
- [4.6-api-documentation-and-protocols.md](./4.6-api-documentation-and-protocols.md) - API文档和协议 ⏳ 待学习
- [4.7-data-storage-layer.md](./4.7-data-storage-layer.md) - 数据存储层 ⏳ 待学习

### 5. 部署 (Deployment)
- [5-deployment.md](./5-deployment.md) - 部署总览 ⏳ 待学习
- [5.1-docker-and-container-deployment.md](./5.1-docker-and-container-deployment.md) - Docker和容器部署 ⏳ 待学习
- [5.2-cicd-pipeline.md](./5.2-cicd-pipeline.md) - CI/CD管道 ⏳ 待学习

### 6. 开发指南 (Development Guide)
- [6-development-guide.md](./6-development-guide.md) - 开发指南总览 ⏳ 待学习
- [6.1-setting-up-development-environment.md](./6.1-setting-up-development-environment.md) - 设置开发环境 ⏳ 待学习
- [6.2-project-structure-and-build-system.md](./6.2-project-structure-and-build-system.md) - 项目结构和构建系统 ⏳ 待学习
- [6.3-testing-and-quality-assurance.md](./6.3-testing-and-quality-assurance.md) - 测试和质量保证 ⏳ 待学习
- [6.4-contributing-guidelines.md](./6.4-contributing-guidelines.md) - 贡献指南 ⏳ 待学习

## 🎯 学习建议

### 学习顺序
1. **概览** → 了解整体架构和技术栈
2. **架构** → 深入理解系统设计
3. **前端** → 学习用户界面实现
4. **后端** → 学习服务端逻辑
5. **部署** → 了解生产环境配置
6. **开发指南** → 掌握开发流程

### 学习方法
- 📖 **理论学习**：仔细阅读每个章节的文档
- 🔍 **源码研究**：点击文档中的源码链接深入研究
- 💻 **动手实践**：尝试运行和修改代码
- 📝 **记录笔记**：记录重要概念和实现细节

## 🚀 复现项目技术映射

### 原项目 → Vue.js + Python 复现

| 原技术栈 | 复现技术栈 | 说明 |
|---------|-----------|------|
| React 18.3.1 | Vue 3 + Composition API | 现代响应式框架 |
| MobX | Pinia | 状态管理 |
| React Router | Vue Router | 客户端路由 |
| Material-UI Joy | Element Plus / Naive UI | UI组件库 |
| Go + Echo | Python + FastAPI | 后端框架 |
| gRPC | FastAPI 自动生成 API | API设计 |
| GoMark | python-markdown | Markdown处理 |
| JWT (Go) | python-jose | 认证方案 |

## 📊 学习进度追踪

- ✅ **已完成**: 2 章节
- ⏳ **待学习**: 24 章节
- 📈 **完成度**: 8% (2/26)

---

**下一步**: 开始学习 [2-architecture.md](./2-architecture.md) - 架构总览
